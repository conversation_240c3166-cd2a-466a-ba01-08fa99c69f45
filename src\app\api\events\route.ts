import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const upcoming = searchParams.get('upcoming')
    const limit = parseInt(searchParams.get('limit') || '20')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-date'
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {}

    if (type) {
      where.type = { equals: type }
    }

    if (category) {
      where.category = { equals: category }
    }

    if (featured === 'true') {
      where.featured = { equals: true }
    }

    if (upcoming === 'true') {
      where.date = { greater_than: new Date().toISOString() }
    }

    if (search) {
      where.or = [
        { title: { like: search } },
        { description: { like: search } },
        { location: { like: search } },
      ]
    }

    // Fetch events
    const result = await payload.find({
      collection: 'events',
      where,
      limit,
      page,
      sort: sort.split(','),
      depth: 2,
    })

    return NextResponse.json({
      events: result.docs,
      totalEvents: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Events GET error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch events',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'events',
      data,
    })

    return NextResponse.json({
      success: true,
      event: result,
    }, { status: 201 })
  } catch (error) {
    console.error('Events POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create event',
      },
      { status: 500 },
    )
  }
}
