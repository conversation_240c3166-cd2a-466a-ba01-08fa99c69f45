'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  NPIS<PERSON>tion,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { N<PERSON><PERSON><PERSON>, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { X, ExternalLink, MapPin, Calendar, Users } from 'lucide-react'
import { useGetPartnersQuery } from '@/lib/api/partnersApi'
import RichText from '@/components/RichText copy'

export interface PartnerResponse {
  docs: Partner[]
  hasNextPage: boolean
  hasPrevPage: boolean
  limit: number
  nextPage: number | null
  page: number
  pagingCounter: number
  prevPage: number | null
  totalDocs: number
  totalPages: number
}

export interface Partner {
  id: number
  name: string
  logo: Logo
  category: string
  location: string
  established: string // ISO date string
  description: string
  fullBio: FullBio
  website?: string
  keyAchievements: KeyAchievement[]
  collaborationAreas: CollaborationArea[]
  partnershipSince: string // ISO date string
  slug: string
  slugLock: boolean
  updatedAt: string
  createdAt: string
}

export interface Logo {
  id: number
  alt: string | null
  caption: string | null
  updatedAt: string
  createdAt: string
  url: string
  thumbnailURL: string | null
  filename: string
  mimeType: string
  filesize: number
  width: number
  height: number
  focalX: number
  focalY: number
  sizes: ImageSizes
}

export interface ImageSizes {
  thumbnail: ImageSize
  square: ImageSize
  small: ImageSize
  medium: ImageSize
  large: ImageSize
  xlarge: ImageSize
  og: ImageSize
}

export interface ImageSize {
  url: string | null
  width: number | null
  height: number | null
  mimeType: string | null
  filesize: number | null
  filename: string | null
}

export interface FullBio {
  root: BioNode
}

export interface BioNode {
  type: string
  format: string
  indent: number
  version: number
  children?: BioNode[]
  mode?: string
  text?: string
  style?: string
  detail?: number
  direction?: string
  textStyle?: string
  textFormat?: number
}

export interface KeyAchievement {
  id: string
  achievement: string
}

export interface CollaborationArea {
  id: string
  area: string
}

interface NPIPartnersShowcaseProps {
  title?: string
  description?: string
}

export const NPIPartnersShowcaseBlock: React.FC<NPIPartnersShowcaseProps> = ({
  title = 'Partner Network',
  description = "Discover the diverse organizations and institutions that collaborate with us to drive sustainable development in Kenya's natural products sector.",
}) => {
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)

  const { data: partners, isLoading, error, refetch } = useGetPartnersQuery({ limit: 1000 })

  const getCardDesign = (index: number) => {
    const designs = [
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-[#25718A]/15 border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
    ]
    return designs[index % designs.length]
  }

  return (
    <NPISection className="bg-white py-16">
      <NPISectionHeader className="mb-12">
        <NPISectionTitle className="text-[#725242] text-4xl font-bold mb-6 font-npi">
          {title}
        </NPISectionTitle>
        <NPISectionDescription className="text-black text-lg font-medium max-w-4xl mx-auto leading-relaxed">
          {description}
        </NPISectionDescription>
      </NPISectionHeader>

      {/* Loading Skeleton */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden rounded-lg bg-white shadow animate-pulse aspect-square flex flex-col"
            >
              {/* Image placeholder */}
              <div className="h-32 w-full bg-gray-200"></div>

              {/* Card header placeholder */}
              <div className="p-3 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>

              {/* Card content placeholder */}
              <div className="p-3 space-y-2 flex-1">
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>

              {/* Footer placeholder */}
              <div className="p-3 flex gap-2">
                <div className="h-8 bg-gray-200 rounded flex-1"></div>
                <div className="h-8 bg-gray-200 rounded w-8"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-red-500 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L4.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-800">Something went wrong</h3>
          <p className="text-gray-600 mt-1">We couldn’t load the partners.</p>
          <button
            onClick={() => refetch()}
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      )}

      {!isLoading && !error && (!partners || partners?.docs?.length === 0) && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 17v-4H5v4H3V7h2v4h4V7h2v10H9zm12 0v-4h-4v4h-2V7h2v4h4V7h2v10h-2z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-800">No partners available</h3>
          <p className="text-gray-600 mt-1">Check back later for new partners.</p>
        </div>
      )}

      {/* Partners Grid - 3 cards per row for better size */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {partners?.docs?.map((partner: Partner, index: number) => {
          const design = getCardDesign(index)
          return (
            <motion.div
              key={partner.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{
                y: -12,
                scale: 1.03,
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                transition: { duration: 0.3, ease: 'easeOut' },
              }}
              className="group"
            >
              <NPICard
                className={`cursor-pointer aspect-square flex flex-col ${design.bg} ${design.border} border-2 transition-all duration-300 overflow-hidden group-hover:border-opacity-80 shadow-lg group-hover:shadow-2xl hover:scale-105`}
                onClick={() => setSelectedPartner(partner)}
              >
                {/* Square card header - minimal */}
                <NPICardHeader className="flex-shrink-0 p-3">
                  <div className="flex flex-col items-center text-center">
                    <div
                      className={`w-16 h-16 ${design.logoContainer} border-2 flex items-center justify-center mb-2 overflow-hidden transition-all duration-300 group-hover:scale-110`}
                    >
                      <Image
                        src={partner?.logo?.url}
                        alt={`${partner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <NPICardTitle
                      className={`text-sm mb-2 leading-tight ${design.text} font-npi font-bold text-center line-clamp-2`}
                    >
                      {partner.name}
                    </NPICardTitle>
                    <span
                      className={`px-2 py-1 text-xs font-semibold ${design.categoryBg} transition-all duration-300`}
                    >
                      {partner.category}
                    </span>
                  </div>
                </NPICardHeader>

                {/* Minimal content for square cards */}
                <NPICardContent className="p-3 pt-0 flex-1 flex flex-col justify-between">
                  <div className="space-y-2">
                    {/* Essential info only */}
                    <div className="text-xs text-[#725242]/70 font-medium">{partner.location}</div>
                    <div className="text-xs text-[#725242]/70 font-medium">
                      Since {new Date(partner.partnershipSince).getFullYear()}
                    </div>
                    <p className={`text-xs leading-tight ${design.text} font-medium line-clamp-3`}>
                      {partner.description}
                    </p>
                  </div>

                  {/* Dynamic action buttons */}
                  <div className="mt-3 space-y-2">
                    <NPIButton
                      size="sm"
                      className="w-full bg-[#8A3E25] hover:bg-[#8A3E25]/90 text-white font-semibold py-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedPartner(partner)
                      }}
                    >
                      View Details
                    </NPIButton>
                    <NPIButton
                      size="sm"
                      variant="outline"
                      className="w-full border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white font-semibold py-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation()
                        if (partner.website) {
                          window.open(partner.website, '_blank')
                        }
                      }}
                    >
                      Contact Partner
                    </NPIButton>
                  </div>
                </NPICardContent>
              </NPICard>
            </motion.div>
          )
        })}
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedPartner && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedPartner(null)}
          >
            <motion.div
              className="bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto border-2 border-[#725242]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="bg-[#8A3E25] p-6 text-white">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 border border-white/30 flex items-center justify-center overflow-hidden">
                      <Image
                        src={selectedPartner?.logo?.url}
                        alt={`${selectedPartner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-1 font-npi">{selectedPartner.name}</h3>
                      <span className="px-3 py-1 bg-white/20 text-sm font-medium">
                        {selectedPartner.category}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedPartner(null)}
                    className="p-2 hover:bg-white/20 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Partner Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">{selectedPartner.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Established {new Date(selectedPartner.established).getFullYear()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Partner since {new Date(selectedPartner.partnershipSince).getFullYear()}
                        </span>
                      </div>
                      {selectedPartner.website && (
                        <div className="flex items-center gap-2">
                          <ExternalLink className="w-4 h-4 text-[#25718A]" />
                          <a
                            href={selectedPartner.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[#25718A] hover:underline transition-colors duration-300"
                          >
                            Visit Website
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Collaboration Areas</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedPartner.collaborationAreas.map((area, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-[#EFE3BA] text-[#725242] text-xs border border-[#725242]/20"
                        >
                          {area.area}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">About</h4>
                  <RichText data={selectedPartner.fullBio} />
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">Key Achievements</h4>
                  <ul className="space-y-2">
                    {selectedPartner.keyAchievements.map((achievement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0"></div>
                        <span className="text-black text-sm">{achievement.achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}
