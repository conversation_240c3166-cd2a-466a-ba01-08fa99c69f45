import type { PayloadRequest } from 'payload'
import { createGenericCRUDHandlers } from './crud-helpers'

// Success Stories CRUD
const successStoriesCRUD = createGenericCRUDHandlers('success-stories', 'Success Story')
export const createSuccessStoryHandler = successStoriesCRUD.createHandler
export const updateSuccessStoryHandler = successStoriesCRUD.updateHandler
export const deleteSuccessStoryHandler = successStoriesCRUD.deleteHandler

// Resources CRUD
const resourcesCRUD = createGenericCRUDHandlers('resources', 'Resource')
export const createResourceHandler = resourcesCRUD.createHandler
export const updateResourceHandler = resourcesCRUD.updateHandler
export const deleteResourceHandler = resourcesCRUD.deleteHandler

// News CRUD
const newsCRUD = createGenericCRUDHandlers('news', 'News Article')
export const createNewsHandler = newsCRUD.createHandler
export const updateNewsHandler = newsCRUD.updateHandler
export const deleteNewsHandler = newsCRUD.deleteHandler

// Media Gallery CRUD
const mediaGalleryCRUD = createGenericCRUDHandlers('media-gallery', 'Media Gallery Item')
export const createMediaGalleryHandler = mediaGalleryCRUD.createHandler
export const updateMediaGalleryHandler = mediaGalleryCRUD.updateHandler
export const deleteMediaGalleryHandler = mediaGalleryCRUD.deleteHandler

// Partnerships CRUD
const partnershipsCRUD = createGenericCRUDHandlers('partnerships', 'Partnership')
export const createPartnershipHandler = partnershipsCRUD.createHandler
export const updatePartnershipHandler = partnershipsCRUD.updateHandler
export const deletePartnershipHandler = partnershipsCRUD.deleteHandler

// Partners CRUD
const partnersCRUD = createGenericCRUDHandlers('partners', 'Partner')
export const createPartnerHandler = partnersCRUD.createHandler
export const updatePartnerHandler = partnersCRUD.updateHandler
export const deletePartnerHandler = partnersCRUD.deleteHandler

// Investment Opportunities CRUD
const investmentOpportunitiesCRUD = createGenericCRUDHandlers('investment-opportunities', 'Investment Opportunity')
export const createInvestmentOpportunityHandler = investmentOpportunitiesCRUD.createHandler
export const updateInvestmentOpportunityHandler = investmentOpportunitiesCRUD.updateHandler
export const deleteInvestmentOpportunityHandler = investmentOpportunitiesCRUD.deleteHandler

// Events CRUD
const eventsCRUD = createGenericCRUDHandlers('events', 'Event')
export const createEventHandler = eventsCRUD.createHandler
export const updateEventHandler = eventsCRUD.updateHandler
export const deleteEventHandler = eventsCRUD.deleteHandler

// Pages CRUD
const pagesCRUD = createGenericCRUDHandlers('pages', 'Page')
export const createPageHandler = pagesCRUD.createHandler
export const updatePageHandler = pagesCRUD.updateHandler
export const deletePageHandler = pagesCRUD.deleteHandler

// Posts CRUD
const postsCRUD = createGenericCRUDHandlers('posts', 'Post')
export const createPostHandler = postsCRUD.createHandler
export const updatePostHandler = postsCRUD.updateHandler
export const deletePostHandler = postsCRUD.deleteHandler

// Categories CRUD
const categoriesCRUD = createGenericCRUDHandlers('categories', 'Category')
export const createCategoryHandler = categoriesCRUD.createHandler
export const updateCategoryHandler = categoriesCRUD.updateHandler
export const deleteCategoryHandler = categoriesCRUD.deleteHandler

// Speakers CRUD
const speakersCRUD = createGenericCRUDHandlers('speakers', 'Speaker')
export const createSpeakerHandler = speakersCRUD.createHandler
export const updateSpeakerHandler = speakersCRUD.updateHandler
export const deleteSpeakerHandler = speakersCRUD.deleteHandler

// Media CRUD
const mediaCRUD = createGenericCRUDHandlers('media', 'Media')
export const createMediaHandler = mediaCRUD.createHandler
export const updateMediaHandler = mediaCRUD.updateHandler
export const deleteMediaHandler = mediaCRUD.deleteHandler
