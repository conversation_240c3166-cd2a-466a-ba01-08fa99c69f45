import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const active = searchParams.get('active')
    const limit = parseInt(searchParams.get('limit') || '20')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || 'name'
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {}

    if (type) {
      where.type = { equals: type }
    }

    if (category) {
      where.category = { equals: category }
    }

    if (featured === 'true') {
      where.featured = { equals: true }
    }

    if (active === 'true') {
      where.active = { equals: true }
    }

    if (search) {
      where.or = [
        { name: { like: search } },
        { description: { like: search } },
        { location: { like: search } },
      ]
    }

    // Fetch partners
    const result = await payload.find({
      collection: 'partners',
      where,
      limit,
      page,
      sort: sort.split(','),
      depth: 2,
    })

    return NextResponse.json({
      partners: result.docs,
      totalPartners: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Partners GET error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch partners',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'partners',
      data,
    })

    return NextResponse.json({
      success: true,
      partner: result,
    }, { status: 201 })
  } catch (error) {
    console.error('Partners POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create partner',
      },
      { status: 500 },
    )
  }
}
