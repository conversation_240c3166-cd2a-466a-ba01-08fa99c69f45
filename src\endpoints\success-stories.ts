import type { PayloadRequest } from 'payload'

interface TransformedSuccessStory {
  id: string
  title: string
  summary: string
  content: string
  image?: TransformedMedia
  gallery?: TransformedMedia[]
  category: string
  location: {
    county?: TransformedCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  participants: {
    beneficiary?: {
      name: string
      role: string
      organization?: string
      photo?: TransformedMedia
    }
    knowledgeHolder?: {
      name: string
      title: string
      expertise?: string
      photo?: TransformedMedia
    }
    supporters?: Array<{
      name: string
      role: string
      organization?: string
    }>
  }
  impact?: {
    metrics?: Array<{
      metric: string
      value: string
      unit?: string
      description?: string
    }>
    beneficiaries?: number
    jobsCreated?: number
    incomeIncrease?: {
      percentage?: number
      amount?: number
      currency: string
    }
  }
  testimonials?: Array<{
    quote: string
    author: string
    role: string
    organization?: string
    photo?: TransformedMedia
  }>
  timeline: {
    startDate: string
    completionDate?: string
    duration?: string
  }
  investment: {
    totalAmount: number
    currency: string
    sources?: Array<{
      source: string
      amount?: number
      type?: string
    }>
  }
  relatedProject?: any
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface SuccessStoriesResponse {
  stories: TransformedSuccessStory[]
  totalStories: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions (reused from projects.ts)
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformSuccessStory = (story: any): TransformedSuccessStory => {
  return {
    id: story.id,
    title: story.title,
    summary: story.summary,
    content: extractTextFromLexical(story.content),
    image: transformMedia(story.image),
    gallery: Array.isArray(story.gallery) 
      ? story.gallery.map((item: any) => transformMedia(item.image)).filter(Boolean)
      : [],
    category: story.category,
    location: {
      county: transformCounty(story.location?.county),
      specificLocation: story.location?.specificLocation,
      coordinates: story.location?.coordinates,
    },
    participants: {
      beneficiary: story.participants?.beneficiary ? {
        name: story.participants.beneficiary.name,
        role: story.participants.beneficiary.role,
        organization: story.participants.beneficiary.organization,
        photo: transformMedia(story.participants.beneficiary.photo),
      } : undefined,
      knowledgeHolder: story.participants?.knowledgeHolder ? {
        name: story.participants.knowledgeHolder.name,
        title: story.participants.knowledgeHolder.title,
        expertise: story.participants.knowledgeHolder.expertise,
        photo: transformMedia(story.participants.knowledgeHolder.photo),
      } : undefined,
      supporters: Array.isArray(story.participants?.supporters) 
        ? story.participants.supporters.map((supporter: any) => ({
            name: supporter.name,
            role: supporter.role,
            organization: supporter.organization,
          }))
        : [],
    },
    impact: story.impact ? {
      metrics: Array.isArray(story.impact.metrics) 
        ? story.impact.metrics.map((metric: any) => ({
            metric: metric.metric,
            value: metric.value,
            unit: metric.unit,
            description: metric.description,
          }))
        : [],
      beneficiaries: story.impact.beneficiaries,
      jobsCreated: story.impact.jobsCreated,
      incomeIncrease: story.impact.incomeIncrease ? {
        percentage: story.impact.incomeIncrease.percentage,
        amount: story.impact.incomeIncrease.amount,
        currency: story.impact.incomeIncrease.currency || 'KES',
      } : undefined,
    } : undefined,
    testimonials: Array.isArray(story.testimonials) 
      ? story.testimonials.map((testimonial: any) => ({
          quote: testimonial.quote,
          author: testimonial.author,
          role: testimonial.role,
          organization: testimonial.organization,
          photo: transformMedia(testimonial.photo),
        }))
      : [],
    timeline: {
      startDate: story.timeline?.startDate,
      completionDate: story.timeline?.completionDate,
      duration: story.timeline?.duration,
    },
    investment: {
      totalAmount: story.investment?.totalAmount || 0,
      currency: story.investment?.currency || 'KES',
      sources: Array.isArray(story.investment?.sources) 
        ? story.investment.sources.map((source: any) => ({
            source: source.source,
            amount: source.amount,
            type: source.type,
          }))
        : [],
    },
    relatedProject: story.relatedProject,
    featured: story.featured || false,
    published: story.published !== false,
    tags: Array.isArray(story.tags) 
      ? story.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    slug: story.slug,
    createdAt: story.createdAt,
    updatedAt: story.updatedAt,
  }
}

// Main Success Stories Handler
export const successStoriesHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      category,
      featured,
      county,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (county) {
      where['location.county'] = { equals: county }
    }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch success stories with populated relationships
    const storiesResult = await payload.find({
      collection: 'success-stories',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, related projects, etc.
    })

    // Transform success stories
    const transformedStories: TransformedSuccessStory[] = storiesResult.docs.map(transformSuccessStory)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(storiesResult.totalDocs / currentLimit)

    const response: SuccessStoriesResponse = {
      stories: transformedStories,
      totalStories: storiesResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in success stories endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single success story by ID or slug
export const successStoryByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let story
    try {
      story = await payload.findByID({
        collection: 'success-stories',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'success-stories',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      story = result.docs[0]
    }

    if (!story) {
      return res.status(404).json({
        error: 'Success story not found',
        message: `No success story found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!story.published && !req.user) {
      return res.status(404).json({
        error: 'Success story not found',
        message: 'Success story is not published',
      })
    }

    const transformedStory = transformSuccessStory(story)

    res.status(200).json({
      story: transformedStory,
    })
  } catch (error) {
    console.error('Error in success story by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
