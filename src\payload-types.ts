/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    pages: Page;
    posts: Post;
    media: Media;
    categories: Category;
    users: User;
    events: Event;
    speakers: Speaker;
    counties: County;
    'investment-opportunities': InvestmentOpportunity;
    resources: Resource;
    partners: Partner;
    projects: Project;
    'success-stories': SuccessStory;
    news: News;
    'media-gallery': MediaGallery;
    partnerships: Partnership;
    'contact-submissions': ContactSubmission;
    'partnership-applications': PartnershipApplication;
    redirects: Redirect;
    forms: Form;
    'form-submissions': FormSubmission;
    search: Search;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    pages: PagesSelect<false> | PagesSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    speakers: SpeakersSelect<false> | SpeakersSelect<true>;
    counties: CountiesSelect<false> | CountiesSelect<true>;
    'investment-opportunities': InvestmentOpportunitiesSelect<false> | InvestmentOpportunitiesSelect<true>;
    resources: ResourcesSelect<false> | ResourcesSelect<true>;
    partners: PartnersSelect<false> | PartnersSelect<true>;
    projects: ProjectsSelect<false> | ProjectsSelect<true>;
    'success-stories': SuccessStoriesSelect<false> | SuccessStoriesSelect<true>;
    news: NewsSelect<false> | NewsSelect<true>;
    'media-gallery': MediaGallerySelect<false> | MediaGallerySelect<true>;
    partnerships: PartnershipsSelect<false> | PartnershipsSelect<true>;
    'contact-submissions': ContactSubmissionsSelect<false> | ContactSubmissionsSelect<true>;
    'partnership-applications': PartnershipApplicationsSelect<false> | PartnershipApplicationsSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    forms: FormsSelect<false> | FormsSelect<true>;
    'form-submissions': FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    header: Header;
    footer: Footer;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: string;
  title: string;
  hero: {
    type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact' | 'npiHero';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    links?:
      | {
          link: {
            type?: ('reference' | 'custom') | null;
            newTab?: boolean | null;
            reference?:
              | ({
                  relationTo: 'pages';
                  value: string | Page;
                } | null)
              | ({
                  relationTo: 'posts';
                  value: string | Post;
                } | null);
            url?: string | null;
            label: string;
            /**
             * Choose how the link should be rendered.
             */
            appearance?: ('default' | 'outline') | null;
          };
          id?: string | null;
        }[]
      | null;
    media?: (string | null) | Media;
  };
  layout: (
    | CallToActionBlock
    | ContentBlock
    | MediaBlock
    | ArchiveBlock
    | FormBlock
    | NPIIntroductionBlock
    | NPIMissionVisionBlock
    | NPIStatisticsBlock
    | NPIFeaturedProjectsBlock
    | NPISuccessStoriesBlock
    | NPILatestUpdatesBlock
    | NPIPartnersBlock
  )[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: string;
  title: string;
  heroImage?: (string | null) | Media;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  relatedPosts?: (string | Post)[] | null;
  categories?: (string | Category)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  authors?: (string | User)[] | null;
  populatedAuthors?:
    | {
        id?: string | null;
        name?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  /**
   * Alternative text for accessibility (required for images)
   */
  alt: string;
  /**
   * Optional caption for the media item
   */
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Detailed description of the media content
   */
  description?: string | null;
  /**
   * Category for organizing media files
   */
  category?:
    | (
        | 'general'
        | 'projects'
        | 'events'
        | 'news'
        | 'success-stories'
        | 'resources'
        | 'partners'
        | 'team'
        | 'logos'
        | 'documents'
      )
    | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  credits?: {
    /**
     * Photographer or creator name
     */
    photographer?: string | null;
    /**
     * Organization or agency
     */
    organization?: string | null;
    /**
     * Copyright information
     */
    copyright?: string | null;
    license?: ('all-rights-reserved' | 'cc-by' | 'cc-by-sa' | 'cc-by-nc' | 'cc-by-nc-sa' | 'public-domain') | null;
  };
  usage?: {
    /**
     * Allow public use of this media
     */
    allowPublicUse?: boolean | null;
    /**
     * Require attribution when used
     */
    requireAttribution?: boolean | null;
    /**
     * Allow commercial use
     */
    commercialUse?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    square?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    xlarge?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    hero?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: string;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  parent?: (string | null) | Category;
  breadcrumbs?:
    | {
        doc?: (string | null) | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  /**
   * Full name of the user
   */
  name: string;
  /**
   * User role determines access permissions
   */
  role: 'super-admin' | 'admin' | 'content-manager' | 'editor' | 'user';
  /**
   * The county this user is associated with
   */
  county?: (string | null) | County;
  /**
   * Organization or company the user represents
   */
  organization?: string | null;
  /**
   * Job title or position
   */
  position?: string | null;
  /**
   * Phone number
   */
  phone?: string | null;
  /**
   * Brief biography or description
   */
  bio?: string | null;
  /**
   * Profile picture
   */
  avatar?: (string | null) | Media;
  preferences?: {
    /**
     * Subscribe to newsletter
     */
    newsletter?: boolean | null;
    /**
     * Receive email notifications
     */
    notifications?: boolean | null;
    language?: ('en' | 'sw') | null;
  };
  /**
   * Last login timestamp
   */
  lastLogin?: string | null;
  /**
   * Whether the user account is active
   */
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "counties".
 */
export interface County {
  id: string;
  /**
   * Full name of the county
   */
  name: string;
  /**
   * Unique identifier code for the county (e.g., KE-001)
   */
  code: string;
  coordinates: {
    /**
     * Latitude coordinate (decimal degrees)
     */
    latitude: number;
    /**
     * Longitude coordinate (decimal degrees)
     */
    longitude: number;
  };
  /**
   * General description of the county
   */
  description?: string | null;
  /**
   * Whether this county is currently active in the system
   */
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: string | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'posts' | null;
  categories?: (string | Category)[] | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'posts';
        value: string | Post;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock".
 */
export interface FormBlock {
  form: string | Form;
  enableIntro?: boolean | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'formBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms".
 */
export interface Form {
  id: string;
  title: string;
  fields?:
    | (
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            defaultValue?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'checkbox';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'country';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'email';
          }
        | {
            message?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'number';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            placeholder?: string | null;
            options?:
              | {
                  label: string;
                  value: string;
                  id?: string | null;
                }[]
              | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'select';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'state';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'text';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'textarea';
          }
      )[]
    | null;
  submitButtonLabel?: string | null;
  /**
   * Choose whether to display an on-page message or redirect to a different page after they submit the form.
   */
  confirmationType?: ('message' | 'redirect') | null;
  confirmationMessage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  redirect?: {
    url: string;
  };
  /**
   * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
   */
  emails?:
    | {
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIIntroductionBlock".
 */
export interface NPIIntroductionBlock {
  title?: string | null;
  description?: string | null;
  content?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiIntroduction';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIMissionVisionBlock".
 */
export interface NPIMissionVisionBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiMissionVision';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIStatisticsBlock".
 */
export interface NPIStatisticsBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiStatistics';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIFeaturedProjectsBlock".
 */
export interface NPIFeaturedProjectsBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiFeaturedProjects';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPISuccessStoriesBlock".
 */
export interface NPISuccessStoriesBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiSuccessStories';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPILatestUpdatesBlock".
 */
export interface NPILatestUpdatesBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiLatestUpdates';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIPartnersBlock".
 */
export interface NPIPartnersBlock {
  title?: string | null;
  description?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'npiPartners';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: string;
  title: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  type: 'keynote' | 'panel' | 'workshop' | 'exhibition' | 'breakout';
  date: string;
  startTime?: string | null;
  endTime?: string | null;
  day: number;
  speakers?: (string | Speaker)[] | null;
  location?: string | null;
  downloads?: (string | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers".
 */
export interface Speaker {
  id: string;
  name: string;
  title?: string | null;
  company?: string | null;
  bio?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  photo?: (string | null) | Media;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "investment-opportunities".
 */
export interface InvestmentOpportunity {
  id: string;
  title: string;
  description: string;
  sector?: string | null;
  location?: string | null;
  investmentRange?: string | null;
  expectedROI?: string | null;
  timeline?: string | null;
  riskLevel?: ('Low' | 'Medium' | 'High') | null;
  stage?: ('Concept' | 'Development' | 'Pilot' | 'Scale-up') | null;
  image?: (string | null) | Media;
  keyHighlights?:
    | {
        highlight?: string | null;
        id?: string | null;
      }[]
    | null;
  marketSize?: string | null;
  jobsCreated?: number | null;
  communityImpact?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources".
 */
export interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'report' | 'guide' | 'policy' | 'toolkit' | 'research' | 'video' | 'presentation';
  category: string;
  date: string;
  fileSize?: string | null;
  downloadCount: number;
  featured?: boolean | null;
  url: string;
  previewUrl?: string | null;
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners".
 */
export interface Partner {
  id: string;
  name: string;
  logo: string | Media;
  category: string;
  location: string;
  established: string;
  description: string;
  fullBio: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  website?: string | null;
  keyAchievements: {
    achievement: string;
    id?: string | null;
  }[];
  collaborationAreas: {
    area: string;
    id?: string | null;
  }[];
  partnershipSince: string;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects".
 */
export interface Project {
  id: string;
  /**
   * The project title that will be displayed publicly
   */
  title: string;
  /**
   * Detailed project description with rich text formatting
   */
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief project summary for cards and previews (max 300 characters)
   */
  summary: string;
  /**
   * Main project image for cards and hero sections
   */
  image: string | Media;
  /**
   * Additional project images and media
   */
  gallery?:
    | {
        image: string | Media;
        caption?: string | null;
        id?: string | null;
      }[]
    | null;
  category:
    | 'knowledge-preservation'
    | 'community-empowerment'
    | 'capacity-building'
    | 'research-development'
    | 'policy-advocacy'
    | 'market-development'
    | 'technology-innovation';
  /**
   * Strategic pillar this project aligns with
   */
  pillar:
    | 'indigenous-knowledge'
    | 'community-innovation'
    | 'capacity-building'
    | 'market-development'
    | 'policy-framework';
  status: 'planning' | 'active' | 'completed' | 'on-hold' | 'cancelled';
  location?: {
    /**
     * Counties where this project is implemented
     */
    counties?: (string | County)[] | null;
    /**
     * Specific location details (e.g., "Baringo County, Marigat Sub-County")
     */
    specificLocation?: string | null;
    /**
     * GPS coordinates for mapping (optional)
     */
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  timeline: {
    startDate: string;
    endDate?: string | null;
    /**
     * Human-readable duration (e.g., "2022-2025", "18 months")
     */
    duration?: string | null;
    milestones?:
      | {
          title: string;
          description?: string | null;
          targetDate?: string | null;
          completed?: boolean | null;
          id?: string | null;
        }[]
      | null;
  };
  budget?: {
    /**
     * Total project budget in KES
     */
    totalBudget?: number | null;
    currency?: ('KES' | 'USD' | 'EUR') | null;
    fundingSources?:
      | {
          source: string;
          amount?: number | null;
          percentage?: number | null;
          id?: string | null;
        }[]
      | null;
  };
  impact?: {
    /**
     * Number of direct beneficiaries
     */
    beneficiaries?: number | null;
    /**
     * Number of communities reached
     */
    communities?: number | null;
    jobsCreated?: number | null;
    metrics?:
      | {
          metric: string;
          value: string;
          unit?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  team?: {
    projectManager?: string | null;
    implementingPartners?:
      | {
          partner?: (string | null) | Partner;
          role?: string | null;
          id?: string | null;
        }[]
      | null;
    keyPersonnel?:
      | {
          name: string;
          role: string;
          organization?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  resources?: {
    documents?:
      | {
          title: string;
          file: string | Media;
          type?:
            | (
                | 'proposal'
                | 'progress-report'
                | 'final-report'
                | 'research-paper'
                | 'policy-brief'
                | 'training-material'
                | 'other'
              )
            | null;
          id?: string | null;
        }[]
      | null;
    links?:
      | {
          title: string;
          url: string;
          description?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  /**
   * Feature this project on homepage and key sections
   */
  featured?: boolean | null;
  /**
   * Make this project visible to the public
   */
  published?: boolean | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "success-stories".
 */
export interface SuccessStory {
  id: string;
  /**
   * The success story title
   */
  title: string;
  /**
   * Brief story summary for cards and previews (max 300 characters)
   */
  summary: string;
  /**
   * Full success story content with rich text formatting
   */
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Main story image
   */
  image: string | Media;
  /**
   * Additional story images
   */
  gallery?:
    | {
        image: string | Media;
        caption?: string | null;
        id?: string | null;
      }[]
    | null;
  category:
    | 'community-innovation'
    | 'knowledge-preservation'
    | 'economic-empowerment'
    | 'capacity-building'
    | 'market-access'
    | 'technology-adoption'
    | 'policy-impact';
  location: {
    county: string | County;
    /**
     * Specific location details (e.g., "Marigat Sub-County")
     */
    specificLocation?: string | null;
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  participants: {
    beneficiary: {
      name: string;
      role: string;
      organization?: string | null;
      photo?: (string | null) | Media;
    };
    knowledgeHolder: {
      name: string;
      title: string;
      expertise?: string | null;
      photo?: (string | null) | Media;
    };
    supporters?:
      | {
          name: string;
          role: string;
          organization?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  impact?: {
    metrics?:
      | {
          metric: string;
          value: string;
          unit?: string | null;
          description?: string | null;
          id?: string | null;
        }[]
      | null;
    /**
     * Number of direct beneficiaries
     */
    beneficiaries?: number | null;
    jobsCreated?: number | null;
    incomeIncrease?: {
      percentage?: number | null;
      amount?: number | null;
      currency?: ('KES' | 'USD') | null;
    };
  };
  testimonials?:
    | {
        quote: string;
        author: string;
        role: string;
        organization?: string | null;
        photo?: (string | null) | Media;
        id?: string | null;
      }[]
    | null;
  timeline: {
    startDate: string;
    completionDate?: string | null;
    /**
     * Human-readable duration (e.g., "18 months")
     */
    duration?: string | null;
  };
  investment: {
    totalAmount: number;
    currency?: ('KES' | 'USD' | 'EUR') | null;
    sources?:
      | {
          source: string;
          amount?: number | null;
          type?: ('grant' | 'investment' | 'loan' | 'community' | 'government') | null;
          id?: string | null;
        }[]
      | null;
  };
  /**
   * Link to the related project if applicable
   */
  relatedProject?: (string | null) | Project;
  /**
   * Feature this story on homepage and key sections
   */
  featured?: boolean | null;
  /**
   * Make this story visible to the public
   */
  published?: boolean | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news".
 */
export interface News {
  id: string;
  /**
   * The news article title
   */
  title: string;
  /**
   * Optional subtitle or tagline
   */
  subtitle?: string | null;
  /**
   * Brief article summary for cards and previews (max 300 characters)
   */
  summary: string;
  /**
   * Full article content with rich text formatting
   */
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Main article image
   */
  featuredImage: string | Media;
  /**
   * Additional article images
   */
  gallery?:
    | {
        image: string | Media;
        caption?: string | null;
        credit?: string | null;
        id?: string | null;
      }[]
    | null;
  category:
    | 'news'
    | 'press-release'
    | 'blog-post'
    | 'event-update'
    | 'project-update'
    | 'policy-update'
    | 'partnership-announcement'
    | 'success-story'
    | 'research-update';
  status: 'draft' | 'review' | 'published' | 'archived';
  /**
   * When this article should be published
   */
  publishDate: string;
  author: {
    name: string;
    role?: string | null;
    organization?: string | null;
    bio?: string | null;
    photo?: (string | null) | Media;
    email?: string | null;
    socialLinks?:
      | {
          platform?: ('twitter' | 'linkedin' | 'facebook' | 'website') | null;
          url: string;
          id?: string | null;
        }[]
      | null;
  };
  location?: {
    county?: (string | null) | County;
    specificLocation?: string | null;
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  relatedContent?: {
    projects?: (string | Project)[] | null;
    successStories?: (string | SuccessStory)[] | null;
    events?: (string | Event)[] | null;
    resources?: (string | Resource)[] | null;
  };
  seo?: {
    metaTitle?: string | null;
    metaDescription?: string | null;
    keywords?:
      | {
          keyword?: string | null;
          id?: string | null;
        }[]
      | null;
    ogImage?: (string | null) | Media;
  };
  engagement?: {
    allowComments?: boolean | null;
    socialSharing?: boolean | null;
    /**
     * Include in newsletter
     */
    newsletter?: boolean | null;
  };
  analytics?: {
    viewCount?: number | null;
    shareCount?: number | null;
    lastViewed?: string | null;
  };
  /**
   * Feature this article on homepage and key sections
   */
  featured?: boolean | null;
  /**
   * Mark as urgent/breaking news
   */
  urgent?: boolean | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media-gallery".
 */
export interface MediaGallery {
  id: string;
  /**
   * The media item title
   */
  title: string;
  /**
   * Detailed description of the media item
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Brief caption for the media item (max 200 characters)
   */
  caption?: string | null;
  type: 'image' | 'video' | 'audio' | 'document' | 'infographic' | 'presentation';
  /**
   * The main media file
   */
  media: string | Media;
  /**
   * Custom thumbnail (auto-generated if not provided)
   */
  thumbnail?: (string | null) | Media;
  category:
    | 'events'
    | 'projects'
    | 'community-stories'
    | 'training-workshops'
    | 'partnerships'
    | 'research-innovation'
    | 'cultural-heritage'
    | 'natural-products'
    | 'leadership-governance'
    | 'marketing-promotion';
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  location?: {
    county?: (string | null) | County;
    specificLocation?: string | null;
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  /**
   * When the media was originally created/captured
   */
  dateCreated: string;
  /**
   * Related event if applicable
   */
  event?: (string | null) | Event;
  /**
   * Related project if applicable
   */
  project?: (string | null) | Project;
  credits?: {
    photographer?: string | null;
    videographer?: string | null;
    organization?: string | null;
    copyright?: string | null;
    license?: ('all-rights-reserved' | 'cc-by' | 'cc-by-sa' | 'cc-by-nc' | 'cc-by-nc-sa' | 'public-domain') | null;
  };
  technical?: {
    dimensions?: {
      width?: number | null;
      height?: number | null;
    };
    /**
     * Duration for video/audio files (e.g., "2:30")
     */
    duration?: string | null;
    /**
     * File size (e.g., "2.5 MB")
     */
    fileSize?: string | null;
    /**
     * File format (e.g., "JPEG", "MP4", "PDF")
     */
    format?: string | null;
    quality?: ('low' | 'medium' | 'high' | 'ultra-high') | null;
  };
  accessibility?: {
    /**
     * Alternative text for screen readers
     */
    altText?: string | null;
    /**
     * Transcript for video/audio content
     */
    transcript?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    /**
     * Subtitle file for videos
     */
    subtitles?: (string | null) | Media;
  };
  usage?: {
    allowDownload?: boolean | null;
    allowEmbedding?: boolean | null;
    commercialUse?: boolean | null;
    /**
     * Required attribution text
     */
    attribution?: string | null;
  };
  analytics?: {
    viewCount?: number | null;
    downloadCount?: number | null;
    shareCount?: number | null;
    lastViewed?: string | null;
  };
  /**
   * Feature this media item in galleries and showcases
   */
  featured?: boolean | null;
  /**
   * Make this media item visible to the public
   */
  published?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnerships".
 */
export interface Partnership {
  id: string;
  /**
   * The partnership title or name
   */
  title: string;
  /**
   * Detailed partnership description
   */
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief partnership summary for cards and previews (max 300 characters)
   */
  summary: string;
  type:
    | 'strategic'
    | 'implementation'
    | 'funding'
    | 'research'
    | 'technology'
    | 'community'
    | 'government'
    | 'academic'
    | 'private-sector';
  status: 'proposed' | 'negotiation' | 'active' | 'completed' | 'on-hold' | 'terminated';
  /**
   * The partner organization
   */
  partner: string | Partner;
  /**
   * Partnership image or logo
   */
  image?: (string | null) | Media;
  timeline: {
    startDate: string;
    endDate?: string | null;
    /**
     * Human-readable duration (e.g., "3 years", "Ongoing")
     */
    duration?: string | null;
    /**
     * Next renewal or review date
     */
    renewalDate?: string | null;
  };
  scope?: {
    objectives?:
      | {
          objective: string;
          id?: string | null;
        }[]
      | null;
    activities?:
      | {
          activity: string;
          description?: string | null;
          status?: ('planned' | 'in-progress' | 'completed' | 'cancelled') | null;
          id?: string | null;
        }[]
      | null;
    deliverables?:
      | {
          deliverable: string;
          dueDate?: string | null;
          completed?: boolean | null;
          id?: string | null;
        }[]
      | null;
  };
  resources?: {
    npiContribution?: {
      /**
       * Financial contribution in KES
       */
      financial?: number | null;
      inKind?:
        | {
            resource: string;
            value?: number | null;
            id?: string | null;
          }[]
        | null;
      personnel?:
        | {
            name: string;
            role: string;
            timeCommitment?: string | null;
            id?: string | null;
          }[]
        | null;
    };
    partnerContribution?: {
      /**
       * Financial contribution in KES
       */
      financial?: number | null;
      inKind?:
        | {
            resource: string;
            value?: number | null;
            id?: string | null;
          }[]
        | null;
      expertise?:
        | {
            area: string;
            description?: string | null;
            id?: string | null;
          }[]
        | null;
    };
  };
  impact?: {
    /**
     * Number of direct beneficiaries
     */
    beneficiaries?: number | null;
    /**
     * Number of communities reached
     */
    communities?: number | null;
    /**
     * Counties where partnership operates
     */
    counties?: (string | County)[] | null;
    metrics?:
      | {
          metric: string;
          target?: string | null;
          achieved?: string | null;
          unit?: string | null;
          id?: string | null;
        }[]
      | null;
    outcomes?:
      | {
          outcome: string;
          description?: string | null;
          evidence?: (string | null) | Media;
          id?: string | null;
        }[]
      | null;
  };
  governance?: {
    agreementType?:
      | ('mou' | 'service-agreement' | 'grant-agreement' | 'joint-venture' | 'letter-of-intent' | 'other')
      | null;
    /**
     * Partnership agreement document
     */
    agreementDocument?: (string | null) | Media;
    keyContacts?:
      | {
          name: string;
          role: string;
          organization?: ('npi' | 'partner') | null;
          email?: string | null;
          phone?: string | null;
          id?: string | null;
        }[]
      | null;
    reportingSchedule?: ('monthly' | 'quarterly' | 'bi-annually' | 'annually' | 'as-needed') | null;
  };
  /**
   * Projects associated with this partnership
   */
  relatedProjects?: (string | Project)[] | null;
  documents?:
    | {
        title: string;
        file: string | Media;
        type?: ('agreement' | 'report' | 'proposal' | 'presentation' | 'other') | null;
        confidential?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Feature this partnership on homepage and key sections
   */
  featured?: boolean | null;
  /**
   * Make this partnership visible to the public
   */
  published?: boolean | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "contact-submissions".
 */
export interface ContactSubmission {
  id: string;
  /**
   * Full name of the person submitting
   */
  name: string;
  /**
   * Email address for response
   */
  email: string;
  /**
   * Phone number (optional)
   */
  phone?: string | null;
  /**
   * Organization or company name (optional)
   */
  organization?: string | null;
  /**
   * Role or position (optional)
   */
  role?: string | null;
  /**
   * Subject of the inquiry
   */
  subject: string;
  category:
    | 'general'
    | 'partnership'
    | 'investment'
    | 'project-collaboration'
    | 'media-press'
    | 'research'
    | 'training'
    | 'technical-support'
    | 'policy-advocacy'
    | 'community-engagement'
    | 'feedback'
    | 'complaint'
    | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  /**
   * Detailed message or inquiry
   */
  message: string;
  location?: {
    county?: (string | null) | County;
    city?: string | null;
    country?: string | null;
  };
  /**
   * Supporting documents or files
   */
  attachments?:
    | {
        file: string | Media;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Current status of the submission
   */
  status: 'new' | 'in-progress' | 'pending-response' | 'resolved' | 'closed' | 'escalated';
  /**
   * Staff member assigned to handle this submission
   */
  assignedTo?: (string | null) | User;
  /**
   * Department responsible for handling this submission
   */
  department?:
    | (
        | 'general-management'
        | 'partnerships'
        | 'projects'
        | 'research-development'
        | 'communications'
        | 'finance'
        | 'technical-support'
        | 'community-engagement'
      )
    | null;
  /**
   * Responses and communications log
   */
  responses?:
    | {
        respondent: string | User;
        responseDate: string;
        responseMethod?: ('email' | 'phone' | 'in-person' | 'video-call' | 'letter') | null;
        response: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        };
        followUpRequired?: boolean | null;
        followUpDate?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Internal notes and comments (not visible to submitter)
   */
  internalNotes?:
    | {
        author: string | User;
        date: string;
        note: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        };
        confidential?: boolean | null;
        id?: string | null;
      }[]
    | null;
  followUp?: {
    required?: boolean | null;
    dueDate?: string | null;
    assignedTo?: (string | null) | User;
    notes?: string | null;
    completed?: boolean | null;
    completedDate?: string | null;
  };
  satisfaction?: {
    /**
     * Satisfaction rating (1-5 stars)
     */
    rating?: number | null;
    /**
     * Feedback on service quality
     */
    feedback?: string | null;
    surveyDate?: string | null;
  };
  metadata?: {
    source?:
      | ('website-form' | 'email' | 'phone' | 'in-person' | 'social-media' | 'event' | 'referral' | 'other')
      | null;
    /**
     * IP address of submitter (auto-captured)
     */
    ipAddress?: string | null;
    /**
     * Browser/device information (auto-captured)
     */
    userAgent?: string | null;
    /**
     * Referring page URL (auto-captured)
     */
    referrer?: string | null;
  };
  /**
   * Archive this submission
   */
  archived?: boolean | null;
  /**
   * Tags for categorization and search
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnership-applications".
 */
export interface PartnershipApplication {
  id: string;
  /**
   * Name of the applying organization
   */
  organizationName: string;
  organizationType:
    | 'private-company'
    | 'ngo-nonprofit'
    | 'government-agency'
    | 'academic-institution'
    | 'international-organization'
    | 'foundation'
    | 'cooperative'
    | 'other';
  /**
   * Organization website URL
   */
  website?: string | null;
  /**
   * Year the organization was established
   */
  establishedYear?: string | null;
  /**
   * Primary contact person name
   */
  contactName: string;
  /**
   * Contact person job title
   */
  contactTitle?: string | null;
  /**
   * Contact email address
   */
  email: string;
  /**
   * Contact phone number
   */
  phone?: string | null;
  partnershipModel:
    | 'strategic-partnership'
    | 'investment-partnership'
    | 'technical-partnership'
    | 'community-partnership'
    | 'research-partnership';
  investmentCapacity?: ('under-1m' | '1m-5m' | '5m-20m' | '20m-50m' | '50m-100m' | 'over-100m') | null;
  /**
   * Areas of project interest or specific projects
   */
  projectInterest?: string | null;
  timeline?: ('immediate' | 'short-term' | 'medium-term' | 'long-term' | 'strategic') | null;
  /**
   * Relevant experience and background
   */
  experience?: string | null;
  /**
   * Partnership objectives and goals
   */
  objectives?: string | null;
  /**
   * Any additional information
   */
  additionalInfo?: string | null;
  status: 'new' | 'under-review' | 'in-discussion' | 'approved' | 'rejected' | 'on-hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  /**
   * Staff member assigned to handle this application
   */
  assignedTo?: (string | null) | User;
  /**
   * Internal notes and communication history
   */
  notes?:
    | {
        note: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        };
        author: string | User;
        date: string;
        type?: ('general' | 'follow-up' | 'decision' | 'meeting') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Submission metadata for tracking
   */
  metadata?: {
    source?: string | null;
    ipAddress?: string | null;
    userAgent?: string | null;
    referrer?: string | null;
  };
  /**
   * Archive this application
   */
  archived?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: string;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: string | Post;
        } | null);
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions".
 */
export interface FormSubmission {
  id: string;
  form: string | Form;
  submissionData?:
    | {
        field: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: string;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'posts';
    value: string | Post;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (string | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        categoryID?: string | null;
        title?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: string;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'pages';
        value: string | Page;
      } | null)
    | ({
        relationTo: 'posts';
        value: string | Post;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'categories';
        value: string | Category;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'events';
        value: string | Event;
      } | null)
    | ({
        relationTo: 'speakers';
        value: string | Speaker;
      } | null)
    | ({
        relationTo: 'counties';
        value: string | County;
      } | null)
    | ({
        relationTo: 'investment-opportunities';
        value: string | InvestmentOpportunity;
      } | null)
    | ({
        relationTo: 'resources';
        value: string | Resource;
      } | null)
    | ({
        relationTo: 'partners';
        value: string | Partner;
      } | null)
    | ({
        relationTo: 'projects';
        value: string | Project;
      } | null)
    | ({
        relationTo: 'success-stories';
        value: string | SuccessStory;
      } | null)
    | ({
        relationTo: 'news';
        value: string | News;
      } | null)
    | ({
        relationTo: 'media-gallery';
        value: string | MediaGallery;
      } | null)
    | ({
        relationTo: 'partnerships';
        value: string | Partnership;
      } | null)
    | ({
        relationTo: 'contact-submissions';
        value: string | ContactSubmission;
      } | null)
    | ({
        relationTo: 'partnership-applications';
        value: string | PartnershipApplication;
      } | null)
    | ({
        relationTo: 'redirects';
        value: string | Redirect;
      } | null)
    | ({
        relationTo: 'forms';
        value: string | Form;
      } | null)
    | ({
        relationTo: 'form-submissions';
        value: string | FormSubmission;
      } | null)
    | ({
        relationTo: 'search';
        value: string | Search;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hero?:
    | T
    | {
        type?: T;
        richText?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        media?: T;
      };
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        formBlock?: T | FormBlockSelect<T>;
        npiIntroduction?: T | NPIIntroductionBlockSelect<T>;
        npiMissionVision?: T | NPIMissionVisionBlockSelect<T>;
        npiStatistics?: T | NPIStatisticsBlockSelect<T>;
        npiFeaturedProjects?: T | NPIFeaturedProjectsBlockSelect<T>;
        npiSuccessStories?: T | NPISuccessStoriesBlockSelect<T>;
        npiLatestUpdates?: T | NPILatestUpdatesBlockSelect<T>;
        npiPartners?: T | NPIPartnersBlockSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  categories?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock_select".
 */
export interface FormBlockSelect<T extends boolean = true> {
  form?: T;
  enableIntro?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIIntroductionBlock_select".
 */
export interface NPIIntroductionBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  content?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIMissionVisionBlock_select".
 */
export interface NPIMissionVisionBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIStatisticsBlock_select".
 */
export interface NPIStatisticsBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIFeaturedProjectsBlock_select".
 */
export interface NPIFeaturedProjectsBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPISuccessStoriesBlock_select".
 */
export interface NPISuccessStoriesBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPILatestUpdatesBlock_select".
 */
export interface NPILatestUpdatesBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "NPIPartnersBlock_select".
 */
export interface NPIPartnersBlockSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  heroImage?: T;
  content?: T;
  relatedPosts?: T;
  categories?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  authors?: T;
  populatedAuthors?:
    | T
    | {
        id?: T;
        name?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  description?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  credits?:
    | T
    | {
        photographer?: T;
        organization?: T;
        copyright?: T;
        license?: T;
      };
  usage?:
    | T
    | {
        allowPublicUse?: T;
        requireAttribution?: T;
        commercialUse?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        square?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        xlarge?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        hero?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  role?: T;
  county?: T;
  organization?: T;
  position?: T;
  phone?: T;
  bio?: T;
  avatar?: T;
  preferences?:
    | T
    | {
        newsletter?: T;
        notifications?: T;
        language?: T;
      };
  lastLogin?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  type?: T;
  date?: T;
  startTime?: T;
  endTime?: T;
  day?: T;
  speakers?: T;
  location?: T;
  downloads?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers_select".
 */
export interface SpeakersSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  company?: T;
  bio?: T;
  photo?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "counties_select".
 */
export interface CountiesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  description?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "investment-opportunities_select".
 */
export interface InvestmentOpportunitiesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  sector?: T;
  location?: T;
  investmentRange?: T;
  expectedROI?: T;
  timeline?: T;
  riskLevel?: T;
  stage?: T;
  image?: T;
  keyHighlights?:
    | T
    | {
        highlight?: T;
        id?: T;
      };
  marketSize?: T;
  jobsCreated?: T;
  communityImpact?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources_select".
 */
export interface ResourcesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  type?: T;
  category?: T;
  date?: T;
  fileSize?: T;
  downloadCount?: T;
  featured?: T;
  url?: T;
  previewUrl?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners_select".
 */
export interface PartnersSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  category?: T;
  location?: T;
  established?: T;
  description?: T;
  fullBio?: T;
  website?: T;
  keyAchievements?:
    | T
    | {
        achievement?: T;
        id?: T;
      };
  collaborationAreas?:
    | T
    | {
        area?: T;
        id?: T;
      };
  partnershipSince?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects_select".
 */
export interface ProjectsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  summary?: T;
  image?: T;
  gallery?:
    | T
    | {
        image?: T;
        caption?: T;
        id?: T;
      };
  category?: T;
  pillar?: T;
  status?: T;
  location?:
    | T
    | {
        counties?: T;
        specificLocation?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  timeline?:
    | T
    | {
        startDate?: T;
        endDate?: T;
        duration?: T;
        milestones?:
          | T
          | {
              title?: T;
              description?: T;
              targetDate?: T;
              completed?: T;
              id?: T;
            };
      };
  budget?:
    | T
    | {
        totalBudget?: T;
        currency?: T;
        fundingSources?:
          | T
          | {
              source?: T;
              amount?: T;
              percentage?: T;
              id?: T;
            };
      };
  impact?:
    | T
    | {
        beneficiaries?: T;
        communities?: T;
        jobsCreated?: T;
        metrics?:
          | T
          | {
              metric?: T;
              value?: T;
              unit?: T;
              id?: T;
            };
      };
  team?:
    | T
    | {
        projectManager?: T;
        implementingPartners?:
          | T
          | {
              partner?: T;
              role?: T;
              id?: T;
            };
        keyPersonnel?:
          | T
          | {
              name?: T;
              role?: T;
              organization?: T;
              id?: T;
            };
      };
  resources?:
    | T
    | {
        documents?:
          | T
          | {
              title?: T;
              file?: T;
              type?: T;
              id?: T;
            };
        links?:
          | T
          | {
              title?: T;
              url?: T;
              description?: T;
              id?: T;
            };
      };
  featured?: T;
  published?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "success-stories_select".
 */
export interface SuccessStoriesSelect<T extends boolean = true> {
  title?: T;
  summary?: T;
  content?: T;
  image?: T;
  gallery?:
    | T
    | {
        image?: T;
        caption?: T;
        id?: T;
      };
  category?: T;
  location?:
    | T
    | {
        county?: T;
        specificLocation?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  participants?:
    | T
    | {
        beneficiary?:
          | T
          | {
              name?: T;
              role?: T;
              organization?: T;
              photo?: T;
            };
        knowledgeHolder?:
          | T
          | {
              name?: T;
              title?: T;
              expertise?: T;
              photo?: T;
            };
        supporters?:
          | T
          | {
              name?: T;
              role?: T;
              organization?: T;
              id?: T;
            };
      };
  impact?:
    | T
    | {
        metrics?:
          | T
          | {
              metric?: T;
              value?: T;
              unit?: T;
              description?: T;
              id?: T;
            };
        beneficiaries?: T;
        jobsCreated?: T;
        incomeIncrease?:
          | T
          | {
              percentage?: T;
              amount?: T;
              currency?: T;
            };
      };
  testimonials?:
    | T
    | {
        quote?: T;
        author?: T;
        role?: T;
        organization?: T;
        photo?: T;
        id?: T;
      };
  timeline?:
    | T
    | {
        startDate?: T;
        completionDate?: T;
        duration?: T;
      };
  investment?:
    | T
    | {
        totalAmount?: T;
        currency?: T;
        sources?:
          | T
          | {
              source?: T;
              amount?: T;
              type?: T;
              id?: T;
            };
      };
  relatedProject?: T;
  featured?: T;
  published?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news_select".
 */
export interface NewsSelect<T extends boolean = true> {
  title?: T;
  subtitle?: T;
  summary?: T;
  content?: T;
  featuredImage?: T;
  gallery?:
    | T
    | {
        image?: T;
        caption?: T;
        credit?: T;
        id?: T;
      };
  category?: T;
  status?: T;
  publishDate?: T;
  author?:
    | T
    | {
        name?: T;
        role?: T;
        organization?: T;
        bio?: T;
        photo?: T;
        email?: T;
        socialLinks?:
          | T
          | {
              platform?: T;
              url?: T;
              id?: T;
            };
      };
  location?:
    | T
    | {
        county?: T;
        specificLocation?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  relatedContent?:
    | T
    | {
        projects?: T;
        successStories?: T;
        events?: T;
        resources?: T;
      };
  seo?:
    | T
    | {
        metaTitle?: T;
        metaDescription?: T;
        keywords?:
          | T
          | {
              keyword?: T;
              id?: T;
            };
        ogImage?: T;
      };
  engagement?:
    | T
    | {
        allowComments?: T;
        socialSharing?: T;
        newsletter?: T;
      };
  analytics?:
    | T
    | {
        viewCount?: T;
        shareCount?: T;
        lastViewed?: T;
      };
  featured?: T;
  urgent?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media-gallery_select".
 */
export interface MediaGallerySelect<T extends boolean = true> {
  title?: T;
  description?: T;
  caption?: T;
  type?: T;
  media?: T;
  thumbnail?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  location?:
    | T
    | {
        county?: T;
        specificLocation?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  dateCreated?: T;
  event?: T;
  project?: T;
  credits?:
    | T
    | {
        photographer?: T;
        videographer?: T;
        organization?: T;
        copyright?: T;
        license?: T;
      };
  technical?:
    | T
    | {
        dimensions?:
          | T
          | {
              width?: T;
              height?: T;
            };
        duration?: T;
        fileSize?: T;
        format?: T;
        quality?: T;
      };
  accessibility?:
    | T
    | {
        altText?: T;
        transcript?: T;
        subtitles?: T;
      };
  usage?:
    | T
    | {
        allowDownload?: T;
        allowEmbedding?: T;
        commercialUse?: T;
        attribution?: T;
      };
  analytics?:
    | T
    | {
        viewCount?: T;
        downloadCount?: T;
        shareCount?: T;
        lastViewed?: T;
      };
  featured?: T;
  published?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnerships_select".
 */
export interface PartnershipsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  summary?: T;
  type?: T;
  status?: T;
  partner?: T;
  image?: T;
  timeline?:
    | T
    | {
        startDate?: T;
        endDate?: T;
        duration?: T;
        renewalDate?: T;
      };
  scope?:
    | T
    | {
        objectives?:
          | T
          | {
              objective?: T;
              id?: T;
            };
        activities?:
          | T
          | {
              activity?: T;
              description?: T;
              status?: T;
              id?: T;
            };
        deliverables?:
          | T
          | {
              deliverable?: T;
              dueDate?: T;
              completed?: T;
              id?: T;
            };
      };
  resources?:
    | T
    | {
        npiContribution?:
          | T
          | {
              financial?: T;
              inKind?:
                | T
                | {
                    resource?: T;
                    value?: T;
                    id?: T;
                  };
              personnel?:
                | T
                | {
                    name?: T;
                    role?: T;
                    timeCommitment?: T;
                    id?: T;
                  };
            };
        partnerContribution?:
          | T
          | {
              financial?: T;
              inKind?:
                | T
                | {
                    resource?: T;
                    value?: T;
                    id?: T;
                  };
              expertise?:
                | T
                | {
                    area?: T;
                    description?: T;
                    id?: T;
                  };
            };
      };
  impact?:
    | T
    | {
        beneficiaries?: T;
        communities?: T;
        counties?: T;
        metrics?:
          | T
          | {
              metric?: T;
              target?: T;
              achieved?: T;
              unit?: T;
              id?: T;
            };
        outcomes?:
          | T
          | {
              outcome?: T;
              description?: T;
              evidence?: T;
              id?: T;
            };
      };
  governance?:
    | T
    | {
        agreementType?: T;
        agreementDocument?: T;
        keyContacts?:
          | T
          | {
              name?: T;
              role?: T;
              organization?: T;
              email?: T;
              phone?: T;
              id?: T;
            };
        reportingSchedule?: T;
      };
  relatedProjects?: T;
  documents?:
    | T
    | {
        title?: T;
        file?: T;
        type?: T;
        confidential?: T;
        id?: T;
      };
  featured?: T;
  published?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "contact-submissions_select".
 */
export interface ContactSubmissionsSelect<T extends boolean = true> {
  name?: T;
  email?: T;
  phone?: T;
  organization?: T;
  role?: T;
  subject?: T;
  category?: T;
  priority?: T;
  message?: T;
  location?:
    | T
    | {
        county?: T;
        city?: T;
        country?: T;
      };
  attachments?:
    | T
    | {
        file?: T;
        description?: T;
        id?: T;
      };
  status?: T;
  assignedTo?: T;
  department?: T;
  responses?:
    | T
    | {
        respondent?: T;
        responseDate?: T;
        responseMethod?: T;
        response?: T;
        followUpRequired?: T;
        followUpDate?: T;
        id?: T;
      };
  internalNotes?:
    | T
    | {
        author?: T;
        date?: T;
        note?: T;
        confidential?: T;
        id?: T;
      };
  followUp?:
    | T
    | {
        required?: T;
        dueDate?: T;
        assignedTo?: T;
        notes?: T;
        completed?: T;
        completedDate?: T;
      };
  satisfaction?:
    | T
    | {
        rating?: T;
        feedback?: T;
        surveyDate?: T;
      };
  metadata?:
    | T
    | {
        source?: T;
        ipAddress?: T;
        userAgent?: T;
        referrer?: T;
      };
  archived?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnership-applications_select".
 */
export interface PartnershipApplicationsSelect<T extends boolean = true> {
  organizationName?: T;
  organizationType?: T;
  website?: T;
  establishedYear?: T;
  contactName?: T;
  contactTitle?: T;
  email?: T;
  phone?: T;
  partnershipModel?: T;
  investmentCapacity?: T;
  projectInterest?: T;
  timeline?: T;
  experience?: T;
  objectives?: T;
  additionalInfo?: T;
  status?: T;
  priority?: T;
  assignedTo?: T;
  notes?:
    | T
    | {
        note?: T;
        author?: T;
        date?: T;
        type?: T;
        id?: T;
      };
  metadata?:
    | T
    | {
        source?: T;
        ipAddress?: T;
        userAgent?: T;
        referrer?: T;
      };
  archived?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms_select".
 */
export interface FormsSelect<T extends boolean = true> {
  title?: T;
  fields?:
    | T
    | {
        checkbox?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              defaultValue?: T;
              id?: T;
              blockName?: T;
            };
        country?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        email?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              message?: T;
              id?: T;
              blockName?: T;
            };
        number?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        select?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              placeholder?: T;
              options?:
                | T
                | {
                    label?: T;
                    value?: T;
                    id?: T;
                  };
              required?: T;
              id?: T;
              blockName?: T;
            };
        state?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        text?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        textarea?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
      };
  submitButtonLabel?: T;
  confirmationType?: T;
  confirmationMessage?: T;
  redirect?:
    | T
    | {
        url?: T;
      };
  emails?:
    | T
    | {
        emailTo?: T;
        cc?: T;
        bcc?: T;
        replyTo?: T;
        emailFrom?: T;
        subject?: T;
        message?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  form?: T;
  submissionData?:
    | T
    | {
        field?: T;
        value?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        categoryID?: T;
        title?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: string;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: string | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: string | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: string | Post;
        } | null);
    global?: string | null;
    user?: (string | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}