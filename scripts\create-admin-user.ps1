# PowerShell script to create admin user
# Run with: powershell -ExecutionPolicy Bypass -File scripts/create-admin-user.ps1

$adminUser = @{
    name = "Ivy Njoroge"
    email = "<EMAIL>"
    password = "123456"
    role = "admin"
}

$baseUrl = "http://localhost:3000"
$jsonBody = $adminUser | ConvertTo-Json

Write-Host "🚀 Creating Admin User for Ivy Njoroge..." -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/users" -Method POST -ContentType "application/json" -Body $jsonBody
    
    Write-Host "✅ Admin user created successfully!" -ForegroundColor Green
    Write-Host "📧 Email: $($response.doc.email)" -ForegroundColor Cyan
    Write-Host "👤 Name: $($response.doc.name)" -ForegroundColor Cyan
    Write-Host "🔑 Role: $($response.doc.role)" -ForegroundColor Cyan
    Write-Host "🆔 ID: $($response.doc.id)" -ForegroundColor Cyan
    
    Write-Host "`n🎉 Setup completed!" -ForegroundColor Green
    Write-Host "🌐 Admin Panel: $baseUrl/admin" -ForegroundColor Yellow
    Write-Host "📧 Email: $($adminUser.email)" -ForegroundColor Yellow
    Write-Host "🔑 Password: $($adminUser.password)" -ForegroundColor Yellow
    
} catch {
    $errorResponse = $_.Exception.Response
    if ($errorResponse) {
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        $reader.Close()
        
        Write-Host "❌ Failed to create user:" -ForegroundColor Red
        Write-Host $errorContent -ForegroundColor Red
        
        # Check if user already exists
        if ($errorContent -like "*already exists*") {
            Write-Host "ℹ️  User already exists - this is fine!" -ForegroundColor Yellow
            Write-Host "You can login with the existing credentials." -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n💡 Alternative: Try creating the user manually through the admin panel" -ForegroundColor Yellow
    Write-Host "🌐 Go to: $baseUrl/admin" -ForegroundColor Yellow
    Write-Host "📝 If no admin exists, the first user created will be an admin" -ForegroundColor Yellow
}
