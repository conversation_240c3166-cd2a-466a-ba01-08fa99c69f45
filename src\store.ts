import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'

import { investmentOpportunitiesApi } from '@/lib/api/investmentOpportunitiesApi'
import { resourcesApi } from '@/lib/api/resourcesApi'
import { partnersApi } from '@/lib/api/partnersApi'

export const store = configureStore({
  reducer: {
    [investmentOpportunitiesApi.reducerPath]: investmentOpportunitiesApi.reducer,
    [resourcesApi.reducerPath]: resourcesApi.reducer,
    [partnersApi.reducerPath]: partnersApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE', 'persist/REGISTER'],
      },
    }).concat([
      resourcesApi.middleware,
      investmentOpportunitiesApi.middleware,
      partnersApi.middleware,
    ]),
})

// Enable automatic refetching behaviors (e.g. on window refocus)
setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
