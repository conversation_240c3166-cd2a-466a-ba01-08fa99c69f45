import { CollectionConfig } from 'payload'

import { anyone } from '@/access/anyone'
import { authenticated } from '@/access/authenticated'
import { slugField } from '@/fields/slug'

export const InvestmentOpportunities: CollectionConfig = {
  slug: 'investment-opportunities',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'sector', 'location', 'stage', 'riskLevel'],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'sector',
      type: 'text',
    },
    {
      name: 'location',
      type: 'text',
    },
    {
      name: 'investmentRange',
      type: 'text',
    },
    {
      name: 'expectedROI',
      type: 'text',
    },
    {
      name: 'timeline',
      type: 'text',
    },
    {
      name: 'riskLevel',
      type: 'select',
      options: [
        { label: 'Low', value: 'Low' },
        { label: 'Medium', value: 'Medium' },
        { label: 'High', value: 'High' },
      ],
    },
    {
      name: 'stage',
      type: 'select',
      options: [
        { label: 'Concept', value: 'Concept' },
        { label: 'Development', value: 'Development' },
        { label: 'Pilot', value: 'Pilot' },
        { label: 'Scale-up', value: 'Scale-up' },
      ],
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media', // Assuming you have a 'media' collection
    },
    {
      name: 'keyHighlights',
      type: 'array',
      fields: [
        {
          name: 'highlight',
          type: 'text',
        },
      ],
    },
    {
      name: 'marketSize',
      type: 'text',
    },
    {
      name: 'jobsCreated',
      type: 'number',
    },
    {
      name: 'communityImpact',
      type: 'textarea',
    },
    ...slugField(),
  ],
}
