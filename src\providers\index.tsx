'use client'

import React from 'react'

import { Provider as ReduxProvider } from 'react-redux'
import { store } from '@/store'

import { HeaderThemeProvider } from './HeaderTheme'
import { ThemeProvider } from './Theme'

export const Providers: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <ReduxProvider store={store}>
      <ThemeProvider>
        <HeaderThemeProvider>{children}</HeaderThemeProvider>
      </ThemeProvider>
    </ReduxProvider>
  )
}
