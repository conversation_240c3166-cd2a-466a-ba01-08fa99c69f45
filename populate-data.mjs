/**
 * Database Population Script
 * Creates realistic sample data for all Payload CMS collections
 */

const API_BASE_URL = 'http://localhost:3000'

// Helper function to make API calls
async function apiCall(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/api${endpoint}`
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  }

  if (data) {
    options.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(url, options)
    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(`API Error: ${result.message || response.statusText}`)
    }
    
    return result
  } catch (error) {
    console.error(`Error calling ${method} ${endpoint}:`, error.message)
    throw error
  }
}

// Get available media and counties
async function getAvailableResources() {
  const [mediaResponse, countiesResponse] = await Promise.all([
    apiCall('/media'),
    apiCall('/counties')
  ])
  
  return {
    media: mediaResponse.docs || [],
    counties: countiesResponse.counties || []
  }
}

// Main population function
async function populateDatabase() {
  try {
    console.log('🚀 Starting database population...')
    
    // Get available resources
    const { media, counties } = await getAvailableResources()
    console.log(`📸 Found ${media.length} media items`)
    console.log(`🏛️ Found ${counties.length} counties`)

    // Create news articles
    console.log('\n📰 Creating News Articles...')
    
    const newsArticles = [
      {
        title: 'NPI Launches Revolutionary Knowledge Documentation Platform',
        excerpt: 'A new digital platform now provides enhanced access to Kenya\'s traditional knowledge heritage, supporting researchers and communities nationwide.',
        summary: 'Revolutionary digital platform provides access to over 1,200 documented indigenous knowledge assets.',
        description: {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                text: 'The Natural Products Industry Initiative has officially launched its comprehensive Knowledge Documentation Platform, marking a significant milestone in preserving and sharing Kenya\'s rich traditional knowledge heritage.'
              }]
            }]
          }
        },
        image: media[0]?.id,
        author: 'Dr. Sarah Kiprotich',
        publishedAt: '2024-01-15T09:00:00.000Z',
        category: 'platform-launch',
        type: 'article',
        status: 'published',
        priority: 'high',
        featured: true,
        urgent: false,
        tags: [
          { tag: 'platform launch' },
          { tag: 'traditional knowledge' },
          { tag: 'technology' }
        ]
      },
      {
        title: 'Baringo Aloe Cooperative Wins National Innovation Award',
        excerpt: 'The Chepkemoi Women\'s Cooperative receives recognition for outstanding innovation in traditional product commercialization.',
        summary: 'Chepkemoi Women\'s Cooperative recognized for transforming traditional aloe vera knowledge into successful business.',
        description: {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                text: 'The Chepkemoi Women\'s Cooperative from Baringo County has been awarded the prestigious National Innovation Award for their outstanding work in commercializing traditional aloe vera products.'
              }]
            }]
          }
        },
        image: media[1]?.id,
        author: 'James Mwangi',
        publishedAt: '2024-02-20T14:30:00.000Z',
        category: 'awards',
        type: 'article',
        status: 'published',
        priority: 'medium',
        featured: true,
        urgent: false,
        tags: [
          { tag: 'awards' },
          { tag: 'innovation' },
          { tag: 'women empowerment' }
        ]
      },
      {
        title: 'International Investment Forum Attracts $50M Commitments',
        excerpt: 'NPI\'s first International Natural Products Investment Forum successfully attracts significant funding commitments for community-based projects.',
        summary: 'First International Investment Forum generates $50M in funding commitments for natural products development.',
        description: {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                text: 'The inaugural International Natural Products Investment Forum hosted by NPI has exceeded expectations, attracting over $50 million in funding commitments from international investors and development partners.'
              }]
            }]
          }
        },
        image: media[2]?.id,
        author: 'Patricia Wanjiku',
        publishedAt: '2023-12-20T16:00:00.000Z',
        category: 'investment',
        type: 'article',
        status: 'published',
        priority: 'high',
        featured: false,
        urgent: true,
        tags: [
          { tag: 'investment' },
          { tag: 'forum' },
          { tag: 'funding' }
        ]
      }
    ]
    
    for (const article of newsArticles) {
      try {
        const result = await apiCall('/news', 'POST', article)
        console.log(`✅ Created news article: ${article.title}`)
      } catch (error) {
        console.error(`❌ Failed to create news article: ${article.title}`, error.message)
      }
    }

    console.log('\n🎉 Database population completed!')
    
  } catch (error) {
    console.error('💥 Database population failed:', error)
    process.exit(1)
  }
}

// Run the script
populateDatabase()
