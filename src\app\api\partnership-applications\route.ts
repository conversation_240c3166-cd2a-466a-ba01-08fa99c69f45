import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { createPartnershipApplicationHandler } from '@/endpoints/partnership-applications'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      body,
      headers: {
        get: (name: string) => request.headers.get(name),
        'user-agent': request.headers.get('user-agent'),
        'x-forwarded-for': request.headers.get('x-forwarded-for'),
        referer: request.headers.get('referer'),
        referrer: request.headers.get('referrer'),
      },
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
      connection: {
        remoteAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await createPartnershipApplicationHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Partnership applications API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to submit partnership application'
      },
      { status: 500 }
    )
  }
}
