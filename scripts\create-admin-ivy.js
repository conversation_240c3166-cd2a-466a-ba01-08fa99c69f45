// Simple script to create admin user for <PERSON> Njoroge
// Run with: node scripts/create-admin-ivy.js

const ADMIN_USER = {
  name: '<PERSON>',
  email: '<EMAIL>',
  password: '123456',
  role: 'admin'
}

const BASE_URL = 'http://localhost:3000'

async function createAdminUser() {
  try {
    console.log('🚀 Creating Admin User for Ivy Njoroge...')
    console.log('==========================================')
    
    // First, try to create the user via API
    const response = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ADMIN_USER)
    })
    
    if (response.ok) {
      const userData = await response.json()
      console.log('✅ Admin user created successfully!')
      console.log(`📧 Email: ${userData.doc.email}`)
      console.log(`👤 Name: ${userData.doc.name}`)
      console.log(`🔑 Role: ${userData.doc.role}`)
      console.log(`🆔 ID: ${userData.doc.id}`)
    } else {
      const errorData = await response.json()
      console.log('❌ Failed to create user via API')
      console.log('Response:', errorData)
      
      // If user already exists, that's okay
      if (errorData.errors && errorData.errors.some(err => err.message.includes('already exists'))) {
        console.log('ℹ️  User already exists - this is fine!')
        console.log('You can login with the existing credentials.')
      }
    }
    
    console.log('\n🎉 Setup completed!')
    console.log(`🌐 Admin Panel: ${BASE_URL}/admin`)
    console.log(`📧 Email: ${ADMIN_USER.email}`)
    console.log(`🔑 Password: ${ADMIN_USER.password}`)
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    console.log('\n💡 Alternative: Try creating the user manually through the admin panel')
    console.log(`🌐 Go to: ${BASE_URL}/admin`)
    console.log('📝 If no admin exists, the first user created will be an admin')
  }
}

// Run the script
createAdminUser()
