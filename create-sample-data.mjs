/**
 * Simple script to create sample data via direct API calls
 */

const API_BASE_URL = 'http://localhost:3000'

async function createProject(projectData) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(projectData)
    })
    
    const result = await response.json()
    
    if (response.ok) {
      console.log(`✅ Created project: ${projectData.title}`)
      return result
    } else {
      console.error(`❌ Failed to create project: ${projectData.title}`, result)
      return null
    }
  } catch (error) {
    console.error(`❌ Error creating project: ${projectData.title}`, error.message)
    return null
  }
}

async function createNewsArticle(articleData) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/news`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(articleData)
    })
    
    const result = await response.json()
    
    if (response.ok) {
      console.log(`✅ Created news article: ${articleData.title}`)
      return result
    } else {
      console.error(`❌ Failed to create news article: ${articleData.title}`, result)
      return null
    }
  } catch (error) {
    console.error(`❌ Error creating news article: ${articleData.title}`, error.message)
    return null
  }
}

async function main() {
  console.log('🚀 Creating sample data...')
  
  // Get available media
  const mediaResponse = await fetch(`${API_BASE_URL}/api/media`)
  const mediaData = await mediaResponse.json()
  const media = mediaData.docs || []
  
  console.log(`📸 Found ${media.length} media items`)
  
  // Create a few more projects
  const projects = [
    {
      title: 'Community Herbal Medicine Certification Program',
      description: {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Comprehensive certification program for traditional herbal medicine practitioners, ensuring quality standards while preserving traditional knowledge.'
            }]
          }]
        }
      },
      summary: 'Certification program for traditional herbal medicine practitioners ensuring quality standards.',
      image: media[1]?.id,
      category: 'capacity-building',
      pillar: 'indigenous-knowledge',
      status: 'active',
      timeline: {
        startDate: '2023-08-01T00:00:00.000Z',
        endDate: '2025-07-31T00:00:00.000Z',
        duration: '24 months'
      },
      budget: {
        totalBudget: 7500000,
        currency: 'KES'
      },
      featured: true,
      published: true,
      tags: [
        { tag: 'herbal medicine' },
        { tag: 'certification' },
        { tag: 'quality standards' }
      ]
    },
    {
      title: 'Natural Products Export Quality Enhancement',
      description: {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Initiative to enhance the quality and international competitiveness of Kenyan natural products for export markets.'
            }]
          }]
        }
      },
      summary: 'Enhancing quality and international competitiveness of Kenyan natural products for export.',
      image: media[2]?.id,
      category: 'market-development',
      pillar: 'market-development',
      status: 'planning',
      timeline: {
        startDate: '2024-04-01T00:00:00.000Z',
        endDate: '2026-03-31T00:00:00.000Z',
        duration: '24 months'
      },
      budget: {
        totalBudget: 12000000,
        currency: 'KES'
      },
      featured: false,
      published: true,
      tags: [
        { tag: 'export' },
        { tag: 'quality enhancement' },
        { tag: 'international markets' }
      ]
    }
  ]
  
  // Create projects
  for (const project of projects) {
    await createProject(project)
  }
  
  // Create news articles
  const newsArticles = [
    {
      title: 'Kenya Natural Products Export Revenue Reaches Record High',
      excerpt: 'Natural products exports from Kenya reach KES 2.5 billion in 2023, marking a 40% increase from previous year.',
      summary: 'Record-breaking export revenue demonstrates growing international demand for Kenyan natural products.',
      content: {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Kenya\'s natural products sector has achieved unprecedented success with export revenues reaching KES 2.5 billion in 2023, representing a remarkable 40% increase from the previous year. This growth demonstrates the increasing international recognition of Kenya\'s high-quality natural products and the effectiveness of our market development initiatives.'
            }]
          }]
        }
      },
      featuredImage: media[0]?.id,
      author: {
        name: 'Economic Research Team',
        role: 'Research Analyst',
        organization: 'NPI Economic Unit'
      },
      publishDate: '2024-01-10T10:00:00.000Z',
      category: 'news',
      type: 'article',
      status: 'published',
      priority: 'high',
      featured: true,
      urgent: false,
      tags: [
        { tag: 'exports' },
        { tag: 'revenue' },
        { tag: 'economic growth' }
      ]
    },
    {
      title: 'New Research Validates Traditional Medicinal Plants',
      excerpt: 'University of Nairobi study confirms therapeutic properties of 15 traditional medicinal plants used by Kenyan communities.',
      summary: 'Scientific validation of traditional medicinal plants opens new opportunities for pharmaceutical development.',
      content: {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'A groundbreaking study by the University of Nairobi has scientifically validated the therapeutic properties of 15 traditional medicinal plants commonly used by Kenyan communities for centuries. This research opens new opportunities for pharmaceutical development while ensuring communities benefit from their traditional knowledge.'
            }]
          }]
        }
      },
      featuredImage: media[1]?.id,
      author: {
        name: 'Dr. Margaret Wanjiru',
        role: 'Lead Researcher',
        organization: 'University of Nairobi'
      },
      publishDate: '2024-01-25T14:00:00.000Z',
      category: 'research-update',
      type: 'article',
      status: 'published',
      priority: 'medium',
      featured: true,
      urgent: false,
      tags: [
        { tag: 'research' },
        { tag: 'medicinal plants' },
        { tag: 'validation' }
      ]
    }
  ]
  
  for (const article of newsArticles) {
    await createNewsArticle(article)
  }

  // Create more success stories
  console.log('\n🏆 Creating Success Stories...')
  const successStories = [
    {
      title: 'Maasai Traditional Medicine IP Protection Success',
      summary: 'Maasai community successfully registered traditional medicine formulations, securing intellectual property rights and establishing sustainable revenue streams.',
      content: 'The Maasai community in Kajiado County has achieved a landmark victory in protecting their traditional medicine knowledge through successful intellectual property registration. This initiative has secured community rights while establishing sustainable revenue streams.',
      image: media[2]?.id,
      category: 'policy-advocacy',
      location: {
        county: '6899f8b3ae794a929a38704f', // kericho county ID
        specificLocation: 'Kajiado County'
      },
      participants: {
        beneficiary: {
          name: 'Elder Joseph Sankale',
          role: 'Traditional Healer',
          contact: '<EMAIL>'
        },
        knowledgeHolder: {
          name: 'Elder Joseph Sankale',
          title: 'Traditional Medicine Practitioner',
          community: 'Maasai Community'
        }
      },
      impact: {
        metrics: [
          {
            name: 'Patents Registered',
            value: 5,
            unit: 'patents'
          },
          {
            name: 'Community Members Benefiting',
            value: 300,
            unit: 'people'
          }
        ],
        incomeIncrease: {
          percentage: 200,
          amount: 25000,
          currency: 'KES'
        }
      },
      testimonials: [
        {
          quote: 'Our traditional knowledge is now protected by law. We can share it with the world while ensuring our community benefits.',
          author: 'Elder Joseph Sankale',
          role: 'Traditional Healer'
        }
      ],
      timeline: {
        startDate: '2022-06-01T00:00:00.000Z',
        completionDate: '2023-12-31T00:00:00.000Z'
      },
      investment: {
        totalAmount: 3500000,
        currency: 'KES',
        sources: [
          {
            name: 'IP Protection Fund',
            amount: 2500000,
            type: 'grant'
          },
          {
            name: 'Community Contribution',
            amount: 1000000,
            type: 'in-kind'
          }
        ]
      },
      featured: true,
      published: true,
      tags: [
        { tag: 'intellectual property' },
        { tag: 'traditional medicine' },
        { tag: 'maasai' }
      ]
    }
  ]

  for (const story of successStories) {
    try {
      const result = await fetch(`${API_BASE_URL}/api/success-stories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(story)
      })

      if (result.ok) {
        console.log(`✅ Created success story: ${story.title}`)
      } else {
        const error = await result.json()
        console.error(`❌ Failed to create success story: ${story.title}`, error)
      }
    } catch (error) {
      console.error(`❌ Error creating success story: ${story.title}`, error.message)
    }
  }

  console.log('🎉 Sample data creation completed!')
}

main().catch(console.error)
