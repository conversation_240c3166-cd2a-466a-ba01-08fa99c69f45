import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { newsHandler } from '@/endpoints/news'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await newsHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('News API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'news',
      data,
    })

    return NextResponse.json({
      success: true,
      article: result,
    }, { status: 201 })
  } catch (error) {
    console.error('News POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create news article',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'News article ID is required for updates',
        },
        { status: 400 },
      )
    }

    // Update news article
    const result = await payload.update({
      collection: 'news',
      id,
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      message: 'News article updated successfully',
      article: result,
    })
  } catch (error) {
    console.error('News PUT error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update news article',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'News article ID is required for deletion',
        },
        { status: 400 },
      )
    }

    // Delete news article
    await payload.delete({
      collection: 'news',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'News article deleted successfully',
    })
  } catch (error) {
    console.error('News DELETE error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete news article',
      },
      { status: 500 },
    )
  }
}
