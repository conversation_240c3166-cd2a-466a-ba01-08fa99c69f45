// services/newsMediaApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'

export const investmentOpportunitiesApi = createApi({
  reducerPath: 'investmentOpportunitiesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    getInvestmentOpportunities: builder.query<any, Record<string, any>>({
      query: (params = {}) => {
        const queryString = serializeParams(params)
        return `investment-opportunities?${queryString.toString()}`
      },
    }),
  }),
})

export const { useGetInvestmentOpportunitiesQuery } = investmentOpportunitiesApi
