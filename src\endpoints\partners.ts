import type { PayloadRequest } from 'payload'
import type { Partner } from '@/payload-types'

// Transform partner for API response
const transformPartner = (partner: Partner): any => {
  return {
    id: partner.id,
    name: partner.name,
    description: partner.description,
    summary: partner.summary,
    logo: partner.logo,
    coverImage: partner.coverImage,
    type: partner.type,
    category: partner.category,
    status: partner.status,
    contact: partner.contact,
    location: partner.location,
    expertise: partner.expertise,
    collaboration: partner.collaboration,
    featured: partner.featured,
    published: partner.published,
    tags: partner.tags,
    slug: partner.slug,
    createdAt: partner.createdAt,
    updatedAt: partner.updatedAt,
  }
}

// Get all partners
export const partnersHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      type,
      category,
      status,
      featured,
      published = 'true',
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: published === 'true' },
    }

    if (type) where.type = { equals: type }
    if (category) where.category = { equals: category }
    if (status) where.status = { equals: status }
    if (featured === 'true') where.featured = { equals: true }
    if (search) {
      where.or = [
        { name: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch partners with populated relationships
    const partnersResult = await payload.find({
      collection: 'partners',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate relationships
    })

    // Transform partners for API response
    const partners = partnersResult.docs.map(transformPartner)

    res.status(200).json({
      partners,
      totalPartners: partnersResult.totalDocs,
      page: partnersResult.page,
      limit: partnersResult.limit,
      totalPages: partnersResult.totalPages,
      hasNextPage: partnersResult.hasNextPage,
      hasPrevPage: partnersResult.hasPrevPage,
    })
  } catch (error) {
    console.error('Partners handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch partners',
    })
  }
}

// Get single partner by ID or slug
export const partnerByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let partner
    try {
      partner = await payload.findByID({
        collection: 'partners',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'partners',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      partner = result.docs[0]
    }

    if (!partner) {
      return res.status(404).json({
        error: 'Partner not found',
        message: `No partner found with ID or slug: ${id}`,
      })
    }

    res.status(200).json({
      partner: transformPartner(partner),
    })
  } catch (error) {
    console.error('Partner by ID handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch partner',
    })
  }
}
