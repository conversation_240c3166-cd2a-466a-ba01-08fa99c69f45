import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const limit = parseInt(searchParams.get('limit') || '20')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-createdAt'
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      published: { equals: true }
    }

    if (type) {
      where.type = { equals: type }
    }

    if (category) {
      where.category = { equals: category }
    }

    if (featured === 'true') {
      where.featured = { equals: true }
    }

    if (search) {
      where.or = [
        { title: { like: search } },
        { description: { like: search } },
        { tags: { like: search } },
      ]
    }

    // Fetch media gallery items
    const result = await payload.find({
      collection: 'media-gallery',
      where,
      limit,
      page,
      sort: sort.split(','),
      depth: 2,
    })

    return NextResponse.json({
      mediaItems: result.docs,
      totalItems: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Media Gallery GET error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch media items',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'media-gallery',
      data,
    })

    return NextResponse.json({
      success: true,
      mediaItem: result,
    }, { status: 201 })
  } catch (error) {
    console.error('Media Gallery POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create media item',
      },
      { status: 500 },
    )
  }
}
