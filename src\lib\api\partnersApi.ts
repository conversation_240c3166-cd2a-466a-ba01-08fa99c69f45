// services/newsMediaApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'

export const partnersApi = createApi({
  reducerPath: 'partnersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    getPartners: builder.query<any, Record<string, any>>({
      query: (params = {}) => {
        const queryString = serializeParams(params)
        return `partners?${queryString.toString()}`
      },
    }),
  }),
})

export const { useGetPartnersQuery } = partnersApi
