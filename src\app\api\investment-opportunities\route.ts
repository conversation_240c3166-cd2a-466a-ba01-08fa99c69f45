import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const sector = searchParams.get('sector')
    const investmentType = searchParams.get('investmentType')
    const status = searchParams.get('status')
    const featured = searchParams.get('featured')
    const urgent = searchParams.get('urgent')
    const limit = parseInt(searchParams.get('limit') || '20')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-updatedAt'
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      published: { equals: true }
    }

    if (sector) {
      where.sector = { equals: sector }
    }

    if (investmentType) {
      where.investmentType = { equals: investmentType }
    }

    if (status) {
      where.status = { equals: status }
    }

    if (featured === 'true') {
      where.featured = { equals: true }
    }

    if (urgent === 'true') {
      where.urgent = { equals: true }
    }

    if (search) {
      where.or = [
        { title: { like: search } },
        { description: { like: search } },
        { sector: { like: search } },
      ]
    }

    // Fetch investment opportunities
    const result = await payload.find({
      collection: 'investment-opportunities',
      where,
      limit,
      page,
      sort: sort.split(','),
      depth: 2,
    })

    return NextResponse.json({
      opportunities: result.docs,
      totalOpportunities: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Investment Opportunities GET error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch investment opportunities',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'investment-opportunities',
      data,
    })

    return NextResponse.json({
      success: true,
      opportunity: result,
    }, { status: 201 })
  } catch (error) {
    console.error('Investment Opportunities POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create investment opportunity',
      },
      { status: 500 },
    )
  }
}
