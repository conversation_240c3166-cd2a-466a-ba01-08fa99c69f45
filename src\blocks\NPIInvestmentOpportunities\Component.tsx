'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import {
  TrendingUp,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  Target,
  Download,
  Filter,
} from 'lucide-react'
import { useGetInvestmentOpportunitiesQuery } from '@/lib/api/investmentOpportunitiesApi'

interface InvestmentOpportunity {
  id: string
  title: string
  description: string
  sector: string
  location: string
  investmentRange: string
  expectedROI: string
  timeline: string
  riskLevel: 'Low' | 'Medium' | 'High'
  stage: 'Concept' | 'Development' | 'Pilot' | 'Scale-up'
  image: string
  keyHighlights: string[]
  marketSize: string
  jobsCreated: number
  communityImpact: string
}

interface NPIInvestmentOpportunitiesProps {
  title?: string
  description?: string
}

export const NPIInvestmentOpportunitiesBlock: React.FC<NPIInvestmentOpportunitiesProps> = ({
  title = 'Investment Opportunities',
  description = "Discover high-impact investment opportunities in Kenya's natural products sector. From community-based enterprises to innovative technologies, find projects that deliver both financial returns and social impact.",
}) => {
  const [selectedSector, setSelectedSector] = useState('All Sectors')
  const [selectedRisk, setSelectedRisk] = useState('All Risk Levels')
  const [selectedStage, setSelectedStage] = useState('All Stages')

  const {
    data: opportunities,
    isLoading,
    error,
    refetch,
  } = useGetInvestmentOpportunitiesQuery({ limit: 16 })

  console.log({ opportunities, isLoading, error })

  const sectors = [
    'All Sectors',
    ...Array.from(
      new Set(opportunities?.docs?.map((opportunity: InvestmentOpportunity) => opportunity.sector)),
    ),
  ]
  const riskLevels = ['All Risk Levels', 'Low', 'Medium', 'High']
  const stages = ['All Stages', 'Concept', 'Development', 'Pilot', 'Scale-up']

  const filteredOpportunities = opportunities?.docs?.filter(
    (opportunity: InvestmentOpportunity) => {
      return (
        (selectedSector === 'All Sectors' || opportunity.sector === selectedSector) &&
        (selectedRisk === 'All Risk Levels' || opportunity.riskLevel === selectedRisk) &&
        (selectedStage === 'All Stages' || opportunity.stage === selectedStage)
      )
    },
  )

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low':
        return 'bg-npi-green-100 text-npi-green-800'
      case 'Medium':
        return 'bg-npi-gold-100 text-npi-gold-800'
      case 'High':
        return 'bg-npi-burgundy-100 text-npi-burgundy-800'
      default:
        return 'bg-npi-grey-100 text-npi-grey-800'
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Concept':
        return 'bg-npi-brown-100 text-npi-brown-800'
      case 'Development':
        return 'bg-npi-burgundy-100 text-npi-burgundy-800'
      case 'Pilot':
        return 'bg-npi-gold-100 text-npi-gold-800'
      case 'Scale-up':
        return 'bg-npi-green-100 text-npi-green-800'
      default:
        return 'bg-npi-grey-100 text-npi-grey-800'
    }
  }

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter Opportunities:</span>
          </div>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Sector</label>
              <select
                value={selectedSector}
                onChange={(e) => setSelectedSector(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {sectors.map((sector) => (
                  <option key={sector} value={sector}>
                    {sector}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Risk Level</label>
              <select
                value={selectedRisk}
                onChange={(e) => setSelectedRisk(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {riskLevels.map((risk) => (
                  <option key={risk} value={risk}>
                    {risk}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Stage</label>
              <select
                value={selectedStage}
                onChange={(e) => setSelectedStage(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {stages.map((stage) => (
                  <option key={stage} value={stage}>
                    {stage}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Loading Skeleton */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden rounded-lg bg-white shadow animate-pulse aspect-square flex flex-col"
            >
              {/* Image placeholder */}
              <div className="h-32 w-full bg-gray-200"></div>

              {/* Card header placeholder */}
              <div className="p-3 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>

              {/* Card content placeholder */}
              <div className="p-3 space-y-2 flex-1">
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>

              {/* Footer placeholder */}
              <div className="p-3 flex gap-2">
                <div className="h-8 bg-gray-200 rounded flex-1"></div>
                <div className="h-8 bg-gray-200 rounded w-8"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-red-500 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L4.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-800">Something went wrong</h3>
          <p className="text-gray-600 mt-1">We couldn’t load the investment opportunities.</p>
          <button
            onClick={() => refetch()}
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      )}

      {!isLoading &&
        !error &&
        (!filteredOpportunities?.docs || filteredOpportunities.docs.length === 0) && (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-gray-400 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 17v-4H5v4H3V7h2v4h4V7h2v10H9zm12 0v-4h-4v4h-2V7h2v4h4V7h2v10h-2z"
              />
            </svg>
            <h3 className="text-lg font-semibold text-gray-800">No opportunities available</h3>
            <p className="text-gray-600 mt-1">Check back later for new investment opportunities.</p>
          </div>
        )}

      {/* Opportunities Grid */}
      {filteredOpportunities?.docs?.length > 0 && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredOpportunities?.docs?.map((opportunity: InvestmentOpportunity) => (
              <NPICard
                key={opportunity.id}
                className="overflow-hidden hover:shadow-xl transition-all duration-300 aspect-square flex flex-col"
              >
                <div className="relative h-32 w-full flex-shrink-0">
                  <Image
                    src={opportunity.image}
                    alt={opportunity.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-2 left-2 flex gap-1">
                    <span
                      className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}
                    >
                      {opportunity.riskLevel}
                    </span>
                    <span
                      className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${getStageColor(opportunity.stage)}`}
                    >
                      {opportunity.stage}
                    </span>
                  </div>
                </div>

                <NPICardHeader className="p-3 flex-shrink-0">
                  <NPICardTitle className="text-sm leading-tight mb-1">
                    {opportunity.title}
                  </NPICardTitle>
                  <div className="text-xs text-primary font-medium mb-1 font-npi">
                    {opportunity.sector}
                  </div>
                  <p className="text-muted-foreground text-xs leading-tight line-clamp-2 font-npi">
                    {opportunity.description}
                  </p>
                </NPICardHeader>

                <NPICardContent className="p-3 flex-1 flex flex-col">
                  {/* Key Metrics - Compact Grid */}
                  <div className="grid grid-cols-2 gap-1 mb-2 text-xs">
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-3 h-3 text-muted-foreground" />
                      <div>
                        <div className="font-medium font-npi text-xs">
                          {opportunity.investmentRange}
                        </div>
                        <div className="text-muted-foreground text-xs">Investment</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="w-3 h-3 text-muted-foreground" />
                      <div>
                        <div className="font-medium font-npi text-xs">
                          {opportunity.expectedROI}
                        </div>
                        <div className="text-muted-foreground text-xs">ROI</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3 text-muted-foreground" />
                      <div>
                        <div className="font-medium font-npi text-xs">{opportunity.location}</div>
                        <div className="text-muted-foreground text-xs">Location</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3 text-muted-foreground" />
                      <div>
                        <div className="font-medium font-npi text-xs">{opportunity.timeline}</div>
                        <div className="text-muted-foreground text-xs">Timeline</div>
                      </div>
                    </div>
                  </div>

                  {/* Risk & Stage Indicators */}
                  <div className="flex gap-1 mb-2">
                    <span
                      className={`px-2 py-0.5 text-xs font-medium font-npi ${
                        opportunity.riskLevel === 'Low'
                          ? 'bg-green-100 text-green-800'
                          : opportunity.riskLevel === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {opportunity.riskLevel} Risk
                    </span>
                    <span className="px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 font-npi">
                      {opportunity.stage}
                    </span>
                  </div>

                  {/* Impact Metrics - Compact */}
                  <div className="bg-primary/5 p-2 mb-2 flex-1">
                    <h5 className="font-semibold mb-1 text-xs font-npi">Impact:</h5>
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      <div>
                        <div className="font-medium text-primary font-npi text-xs">
                          {opportunity.jobsCreated}
                        </div>
                        <div className="text-muted-foreground text-xs">Jobs</div>
                      </div>
                      <div>
                        <div className="font-medium text-primary font-npi text-xs">
                          {opportunity.marketSize}
                        </div>
                        <div className="text-muted-foreground text-xs">Market</div>
                      </div>
                    </div>
                  </div>

                  {/* Key Highlights - Compact */}
                  <div className="mb-2 flex-1">
                    <h5 className="font-semibold mb-1 text-xs font-npi">Highlights:</h5>
                    <ul className="space-y-0.5">
                      {opportunity.keyHighlights.slice(0, 2).map((highlight, index) => (
                        <li key={index} className="flex items-start gap-1 text-xs">
                          <div className="w-1 h-1 bg-primary rounded-full mt-1.5 flex-shrink-0"></div>
                          <span className="text-muted-foreground font-npi line-clamp-1">
                            {highlight}
                          </span>
                        </li>
                      ))}
                      {opportunity.keyHighlights.length > 2 && (
                        <li className="text-xs text-primary font-npi">
                          +{opportunity.keyHighlights.length - 2} more
                        </li>
                      )}
                    </ul>
                  </div>
                </NPICardContent>

                <NPICardFooter className="flex gap-1 p-3 flex-shrink-0">
                  <NPIButton asChild variant="primary" className="flex-1 text-xs py-1">
                    <Link href={`/partnerships/opportunities/${opportunity.id}`}>View Details</Link>
                  </NPIButton>
                  <NPIButton asChild variant="outline" size="sm" className="px-2">
                    <Link href={`/partnerships/opportunities/${opportunity.id}/prospectus.pdf`}>
                      <Download className="w-3 h-3" />
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            ))}
          </div>

          {/* Results Summary */}
          <div className="text-center mt-8">
            <p className="text-muted-foreground font-npi">
              Showing {filteredOpportunities?.docs?.length} of {opportunities?.docs?.length}{' '}
              investment opportunities
            </p>
          </div>
        </>
      )}
    </NPISection>
  )
}
