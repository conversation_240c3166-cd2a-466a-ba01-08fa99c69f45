// services/newsMediaApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'

export const resourcesApi = createApi({
  reducerPath: 'resourcesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    getResources: builder.query<any, Record<string, any>>({
      query: (params = {}) => {
        const queryString = serializeParams(params)
        return `resources?${queryString.toString()}`
      },
    }),
  }),
})

export const { useGetResourcesQuery } = resourcesApi
