import type { PayloadRequest } from 'payload'

// Generic CRUD handlers for content collections
export const createGenericCRUDHandlers = (collectionSlug: string, collectionName: string) => {
  // Create handler
  const createHandler = async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req

      // Check if user is authenticated (admin access only)
      if (!req.user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: `Authentication required to create ${collectionName.toLowerCase()}`,
        })
      }

      // Extract data from request body
      const data = req.body

      // Create the document
      const document = await payload.create({
        collection: collectionSlug,
        data: {
          ...data,
          published: data.published !== undefined ? data.published : false,
          featured: data.featured !== undefined ? data.featured : false,
        },
      })

      res.status(201).json({
        success: true,
        message: `${collectionName} created successfully`,
        [collectionSlug.replace('-', '')]: document,
      })
    } catch (error) {
      console.error(`Create ${collectionName} handler error:`, error)
      res.status(500).json({
        error: 'Internal server error',
        message: `Failed to create ${collectionName.toLowerCase()}`,
      })
    }
  }

  // Update handler
  const updateHandler = async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const { id } = req.params

      // Check if user is authenticated (admin access only)
      if (!req.user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: `Authentication required to update ${collectionName.toLowerCase()}`,
        })
      }

      const data = req.body

      const updatedDocument = await payload.update({
        collection: collectionSlug,
        id,
        data,
      })

      res.status(200).json({
        success: true,
        message: `${collectionName} updated successfully`,
        [collectionSlug.replace('-', '')]: updatedDocument,
      })
    } catch (error) {
      console.error(`Update ${collectionName} handler error:`, error)
      res.status(500).json({
        error: 'Internal server error',
        message: `Failed to update ${collectionName.toLowerCase()}`,
      })
    }
  }

  // Delete handler
  const deleteHandler = async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const { id } = req.params

      // Check if user is authenticated (admin access only)
      if (!req.user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: `Authentication required to delete ${collectionName.toLowerCase()}`,
        })
      }

      await payload.delete({
        collection: collectionSlug,
        id,
      })

      res.status(200).json({
        success: true,
        message: `${collectionName} deleted successfully`,
      })
    } catch (error) {
      console.error(`Delete ${collectionName} handler error:`, error)
      res.status(500).json({
        error: 'Internal server error',
        message: `Failed to delete ${collectionName.toLowerCase()}`,
      })
    }
  }

  return {
    createHandler,
    updateHandler,
    deleteHandler,
  }
}

// Utility function to extract plain text from Lexical rich text
export const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return richTextData.root.children
      .map((child: any) => {
        if (child.type === 'paragraph' && child.children) {
          return child.children
            .map((textNode: any) => textNode.text || '')
            .join('')
        }
        return ''
      })
      .join('\n')
  }

  return ''
}

// Transform media object for API response
export const transformMedia = (media: any): any => {
  if (!media || typeof media !== 'object') return undefined
  
  return {
    id: media.id,
    url: media.url || '',
    filename: media.filename || '',
    mimeType: media.mimeType || '',
    filesize: media.filesize || 0,
    width: media.width,
    height: media.height,
    alt: media.alt,
  }
}

// Transform county object for API response
export const transformCounty = (county: any): any => {
  if (!county) return undefined
  
  if (typeof county === 'string') {
    return { id: county, name: '', code: '' }
  }
  
  return {
    id: county.id,
    name: county.name || '',
    code: county.code || '',
  }
}
