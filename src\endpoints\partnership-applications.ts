import type { PayloadRequest } from 'payload'
import type { PartnershipApplication } from '@/payload-types'

// Transform partnership application for API response
const transformPartnershipApplication = (application: PartnershipApplication): any => {
  return {
    id: application.id,
    organizationName: application.organizationName,
    organizationType: application.organizationType,
    website: application.website,
    establishedYear: application.establishedYear,
    contactName: application.contactName,
    contactTitle: application.contactTitle,
    email: application.email,
    phone: application.phone,
    partnershipModel: application.partnershipModel,
    investmentCapacity: application.investmentCapacity,
    projectInterest: application.projectInterest,
    timeline: application.timeline,
    experience: application.experience,
    objectives: application.objectives,
    additionalInfo: application.additionalInfo,
    status: application.status,
    priority: application.priority,
    assignedTo: application.assignedTo,
    notes: application.notes,
    metadata: application.metadata,
    archived: application.archived,
    createdAt: application.createdAt,
    updatedAt: application.updatedAt,
  }
}

// Get all partnership applications (Admin only)
export const partnershipApplicationsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access partnership applications',
      })
    }

    // Parse query parameters
    const {
      status,
      priority,
      organizationType,
      partnershipModel,
      assignedTo,
      archived = 'false',
      limit = '20',
      page = '1',
      sort = '-createdAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      archived: { equals: archived === 'true' },
    }

    if (status) where.status = { equals: status }
    if (priority) where.priority = { equals: priority }
    if (organizationType) where.organizationType = { equals: organizationType }
    if (partnershipModel) where.partnershipModel = { equals: partnershipModel }
    if (assignedTo) where.assignedTo = { equals: assignedTo }
    if (search) {
      where.or = [
        { organizationName: { contains: search } },
        { contactName: { contains: search } },
        { email: { contains: search } },
        { projectInterest: { contains: search } },
      ]
    }

    // Fetch applications with populated relationships
    const applicationsResult = await payload.find({
      collection: 'partnership-applications',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate assignedTo and other relationships
    })

    // Transform applications for API response
    const applications = applicationsResult.docs.map(transformPartnershipApplication)

    res.status(200).json({
      applications,
      totalApplications: applicationsResult.totalDocs,
      page: applicationsResult.page,
      limit: applicationsResult.limit,
      totalPages: applicationsResult.totalPages,
      hasNextPage: applicationsResult.hasNextPage,
      hasPrevPage: applicationsResult.hasPrevPage,
    })
  } catch (error) {
    console.error('Partnership applications handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch partnership applications',
    })
  }
}

// Get single partnership application by ID (Admin only)
export const partnershipApplicationByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access partnership applications',
      })
    }

    const application = await payload.findByID({
      collection: 'partnership-applications',
      id,
      depth: 2,
    })

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: `No partnership application found with ID: ${id}`,
      })
    }

    res.status(200).json({
      application: transformPartnershipApplication(application),
    })
  } catch (error) {
    console.error('Partnership application by ID handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch partnership application',
    })
  }
}

// Create new partnership application (Public endpoint)
export const createPartnershipApplicationHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Extract data from request body
    const {
      organizationName,
      organizationType,
      website,
      establishedYear,
      contactName,
      contactTitle,
      email,
      phone,
      partnershipModel,
      investmentCapacity,
      projectInterest,
      timeline,
      experience,
      objectives,
      additionalInfo,
      priority = 'medium',
    } = req.body

    // Basic validation
    if (!organizationName || !organizationType || !contactName || !email || !partnershipModel) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Organization name, type, contact name, email, and partnership model are required',
      })
    }

    // Capture metadata
    const metadata = {
      source: 'website-form',
      ipAddress: req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
      userAgent: req.headers['user-agent'],
      referrer: req.headers.referer || req.headers.referrer,
    }

    // Create the application
    const application = await payload.create({
      collection: 'partnership-applications',
      data: {
        organizationName,
        organizationType,
        website,
        establishedYear,
        contactName,
        contactTitle,
        email,
        phone,
        partnershipModel,
        investmentCapacity,
        projectInterest,
        timeline,
        experience,
        objectives,
        additionalInfo,
        status: 'new',
        priority,
        metadata,
        archived: false,
      },
    })

    // Return success response (without sensitive data)
    res.status(201).json({
      success: true,
      message: 'Partnership application submitted successfully',
      applicationId: application.id,
      status: 'new',
    })
  } catch (error) {
    console.error('Create partnership application handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to submit partnership application',
    })
  }
}

// Update partnership application status (Admin only)
export const updatePartnershipApplicationHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to update partnership applications',
      })
    }

    const { status, assignedTo, priority, archived } = req.body

    const updatedApplication = await payload.update({
      collection: 'partnership-applications',
      id,
      data: {
        status,
        assignedTo,
        priority,
        archived,
      },
    })

    res.status(200).json({
      success: true,
      message: 'Partnership application updated successfully',
      application: transformPartnershipApplication(updatedApplication),
    })
  } catch (error) {
    console.error('Update partnership application handler error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update partnership application',
    })
  }
}
