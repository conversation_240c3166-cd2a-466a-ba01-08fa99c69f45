import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { successStoriesHandler } from '@/endpoints/success-stories'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await successStoriesHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Success Stories API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const data = await request.json()

    const result = await payload.create({
      collection: 'success-stories',
      data,
    })

    return NextResponse.json({
      success: true,
      story: result,
    }, { status: 201 })
  } catch (error) {
    console.error('Success Stories POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create success story',
      },
      { status: 500 },
    )
  }
}
