import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const SuccessStories: CollectionConfig = {
  slug: 'success-stories',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'location', 'featured', 'updatedAt'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Success Story',
    plural: 'Success Stories',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The success story title',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief story summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      admin: {
        description: 'Full success story content with rich text formatting',
      },
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      required: true,
      admin: {
        description: 'Main story image',
      },
    },
    {
      name: 'gallery',
      type: 'array',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
      ],
      admin: {
        description: 'Additional story images',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Community-Led Innovation', value: 'community-innovation' },
        { label: 'Knowledge Preservation', value: 'knowledge-preservation' },
        { label: 'Economic Empowerment', value: 'economic-empowerment' },
        { label: 'Capacity Building', value: 'capacity-building' },
        { label: 'Market Access', value: 'market-access' },
        { label: 'Technology Adoption', value: 'technology-adoption' },
        { label: 'Policy Impact', value: 'policy-impact' },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'county',
          type: 'relationship',
          relationTo: 'counties',
          required: true,
        },
        {
          name: 'specificLocation',
          type: 'text',
          dbName: 'specific_loc',
          admin: {
            description: 'Specific location details (e.g., "Marigat Sub-County")',
          },
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
      ],
    },
    {
      name: 'participants',
      type: 'group',
      fields: [
        {
          name: 'beneficiary',
          type: 'group',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'organization',
              type: 'text',
            },
            {
              name: 'photo',
              type: 'upload',
              relationTo: 'media',
            },
          ],
        },
        {
          name: 'knowledgeHolder',
          type: 'group',
          dbName: 'knowledge_holder',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'expertise',
              type: 'text',
            },
            {
              name: 'photo',
              type: 'upload',
              relationTo: 'media',
            },
          ],
        },
        {
          name: 'supporters',
          type: 'array',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'organization',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'impact',
      type: 'group',
      fields: [
        {
          name: 'metrics',
          type: 'array',
          fields: [
            {
              name: 'metric',
              type: 'text',
              required: true,
            },
            {
              name: 'value',
              type: 'text',
              required: true,
            },
            {
              name: 'unit',
              type: 'text',
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
        {
          name: 'beneficiaries',
          type: 'number',
          admin: {
            description: 'Number of direct beneficiaries',
          },
        },
        {
          name: 'jobsCreated',
          type: 'number',
        },
        {
          name: 'incomeIncrease',
          type: 'group',
          fields: [
            {
              name: 'percentage',
              type: 'number',
            },
            {
              name: 'amount',
              type: 'number',
            },
            {
              name: 'currency',
              type: 'select',
              defaultValue: 'KES',
              options: [
                { label: 'KES', value: 'KES' },
                { label: 'USD', value: 'USD' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'testimonials',
      type: 'array',
      fields: [
        {
          name: 'quote',
          type: 'textarea',
          required: true,
        },
        {
          name: 'author',
          type: 'text',
          required: true,
        },
        {
          name: 'role',
          type: 'text',
          required: true,
        },
        {
          name: 'organization',
          type: 'text',
        },
        {
          name: 'photo',
          type: 'upload',
          relationTo: 'media',
        },
      ],
    },
    {
      name: 'timeline',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          required: true,
        },
        {
          name: 'completionDate',
          type: 'date',
        },
        {
          name: 'duration',
          type: 'text',
          admin: {
            description: 'Human-readable duration (e.g., "18 months")',
          },
        },
      ],
    },
    {
      name: 'investment',
      type: 'group',
      fields: [
        {
          name: 'totalAmount',
          type: 'number',
          required: true,
        },
        {
          name: 'currency',
          type: 'select',
          defaultValue: 'KES',
          options: [
            { label: 'KES', value: 'KES' },
            { label: 'USD', value: 'USD' },
            { label: 'EUR', value: 'EUR' },
          ],
        },
        {
          name: 'sources',
          type: 'array',
          fields: [
            {
              name: 'source',
              type: 'text',
              required: true,
            },
            {
              name: 'amount',
              type: 'number',
            },
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Grant', value: 'grant' },
                { label: 'Investment', value: 'investment' },
                { label: 'Loan', value: 'loan' },
                { label: 'Community Contribution', value: 'community' },
                { label: 'Government Support', value: 'government' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'relatedProject',
      type: 'relationship',
      relationTo: 'projects',
      admin: {
        description: 'Link to the related project if applicable',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this story on homepage and key sections',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this story visible to the public',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
}

export default SuccessStories
