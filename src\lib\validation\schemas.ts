import { z } from 'zod'

// Common validation schemas
export const emailSchema = z.string().email('Please enter a valid email address')

export const phoneSchema = z.string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
  .optional()

export const urlSchema = z.string().url('Please enter a valid URL').optional()

export const slugSchema = z.string()
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must contain only lowercase letters, numbers, and hyphens')

export const richTextSchema = z.object({
  root: z.object({
    children: z.array(z.any()),
    direction: z.string(),
    format: z.string(),
    indent: z.number(),
    type: z.string(),
    version: z.number(),
  }),
})

export const coordinatesSchema = z.object({
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
}).optional()

export const mediaSchema = z.object({
  id: z.string(),
  filename: z.string(),
  url: z.string().url(),
  alt: z.string().optional(),
  width: z.number().optional(),
  height: z.number().optional(),
  mimeType: z.string().optional(),
  filesize: z.number().optional(),
}).optional()

// Project validation schema
export const projectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: richTextSchema,
  summary: z.string().min(1, 'Summary is required').max(300, 'Summary must be less than 300 characters'),
  image: mediaSchema,
  gallery: z.array(z.object({
    image: mediaSchema,
    caption: z.string().optional(),
  })).optional(),
  category: z.enum([
    'knowledge-preservation',
    'community-empowerment',
    'capacity-building',
    'research-development',
    'policy-advocacy',
    'market-development',
    'technology-innovation',
  ]),
  pillar: z.enum([
    'indigenous-knowledge',
    'community-innovation',
    'capacity-building',
    'market-development',
    'policy-framework',
  ]),
  status: z.enum(['planning', 'active', 'completed', 'on-hold', 'cancelled']).default('active'),
  location: z.object({
    counties: z.array(z.string()).optional(),
    specificLocation: z.string().optional(),
    coordinates: coordinatesSchema,
  }).optional(),
  timeline: z.object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime().optional(),
    duration: z.string().optional(),
    milestones: z.array(z.object({
      title: z.string().min(1, 'Milestone title is required'),
      description: z.string().optional(),
      targetDate: z.string().datetime().optional(),
      completed: z.boolean().default(false),
    })).optional(),
  }),
  budget: z.object({
    totalBudget: z.number().positive().optional(),
    currency: z.enum(['KES', 'USD', 'EUR']).default('KES'),
    fundingSources: z.array(z.object({
      source: z.string().min(1, 'Funding source is required'),
      amount: z.number().positive().optional(),
      percentage: z.number().min(0).max(100).optional(),
    })).optional(),
  }).optional(),
  impact: z.object({
    beneficiaries: z.number().int().positive().optional(),
    communities: z.number().int().positive().optional(),
    jobsCreated: z.number().int().positive().optional(),
    metrics: z.array(z.object({
      metric: z.string().min(1, 'Metric name is required'),
      value: z.string().min(1, 'Metric value is required'),
      unit: z.string().optional(),
    })).optional(),
  }).optional(),
  team: z.object({
    projectManager: z.string().optional(),
    implementingPartners: z.array(z.object({
      partner: z.string(),
      role: z.string().optional(),
    })).optional(),
    keyPersonnel: z.array(z.object({
      name: z.string().min(1, 'Name is required'),
      role: z.string().min(1, 'Role is required'),
      organization: z.string().optional(),
    })).optional(),
  }).optional(),
  featured: z.boolean().default(false),
  published: z.boolean().default(true),
  tags: z.array(z.object({
    tag: z.string().min(1, 'Tag cannot be empty'),
  })).optional(),
  slug: slugSchema,
})

// Success Story validation schema
export const successStorySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  summary: z.string().min(1, 'Summary is required').max(300, 'Summary must be less than 300 characters'),
  content: richTextSchema,
  image: mediaSchema,
  gallery: z.array(z.object({
    image: mediaSchema,
    caption: z.string().optional(),
  })).optional(),
  category: z.enum([
    'community-innovation',
    'knowledge-preservation',
    'economic-empowerment',
    'capacity-building',
    'market-access',
    'technology-adoption',
    'policy-impact',
  ]),
  location: z.object({
    county: z.string().optional(),
    specificLocation: z.string().optional(),
    coordinates: coordinatesSchema,
  }).optional(),
  participants: z.object({
    beneficiary: z.object({
      name: z.string().min(1, 'Beneficiary name is required'),
      role: z.string().min(1, 'Beneficiary role is required'),
      organization: z.string().optional(),
      photo: mediaSchema,
    }).optional(),
    knowledgeHolder: z.object({
      name: z.string().min(1, 'Knowledge holder name is required'),
      title: z.string().min(1, 'Knowledge holder title is required'),
      expertise: z.string().optional(),
      photo: mediaSchema,
    }).optional(),
    supporters: z.array(z.object({
      name: z.string().min(1, 'Supporter name is required'),
      role: z.string().min(1, 'Supporter role is required'),
      organization: z.string().optional(),
    })).optional(),
  }),
  impact: z.object({
    metrics: z.array(z.object({
      metric: z.string().min(1, 'Metric name is required'),
      value: z.string().min(1, 'Metric value is required'),
      unit: z.string().optional(),
      description: z.string().optional(),
    })).optional(),
    beneficiaries: z.number().int().positive().optional(),
    jobsCreated: z.number().int().positive().optional(),
    incomeIncrease: z.object({
      percentage: z.number().positive().optional(),
      amount: z.number().positive().optional(),
      currency: z.enum(['KES', 'USD', 'EUR']).default('KES'),
    }).optional(),
  }).optional(),
  testimonials: z.array(z.object({
    quote: z.string().min(1, 'Quote is required'),
    author: z.string().min(1, 'Author is required'),
    role: z.string().min(1, 'Role is required'),
    organization: z.string().optional(),
    photo: mediaSchema,
  })).optional(),
  timeline: z.object({
    startDate: z.string().datetime(),
    completionDate: z.string().datetime().optional(),
    duration: z.string().optional(),
  }),
  investment: z.object({
    totalAmount: z.number().positive(),
    currency: z.enum(['KES', 'USD', 'EUR']).default('KES'),
    sources: z.array(z.object({
      source: z.string().min(1, 'Investment source is required'),
      amount: z.number().positive().optional(),
      type: z.enum(['grant', 'investment', 'loan', 'community', 'government']).optional(),
    })).optional(),
  }),
  relatedProject: z.string().optional(),
  featured: z.boolean().default(false),
  published: z.boolean().default(true),
  tags: z.array(z.object({
    tag: z.string().min(1, 'Tag cannot be empty'),
  })).optional(),
  slug: slugSchema,
})

// Resource validation schema
export const resourceSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: richTextSchema,
  summary: z.string().min(1, 'Summary is required').max(300, 'Summary must be less than 300 characters'),
  type: z.enum([
    'research-report',
    'policy-document',
    'training-guide',
    'best-practices',
    'case-study',
    'technical-manual',
    'presentation',
    'video',
    'infographic',
    'toolkit',
    'white-paper',
    'newsletter',
  ]),
  category: z.enum([
    'indigenous-knowledge',
    'community-development',
    'capacity-building',
    'market-development',
    'policy-governance',
    'research-innovation',
    'sustainability',
    'technology',
    'finance-investment',
  ]),
  file: mediaSchema,
  coverImage: mediaSchema,
  additionalFiles: z.array(z.object({
    title: z.string().min(1, 'File title is required'),
    file: mediaSchema,
    description: z.string().optional(),
  })).optional(),
  metadata: z.object({
    authors: z.array(z.object({
      name: z.string().min(1, 'Author name is required'),
      organization: z.string().optional(),
      role: z.string().optional(),
    })).optional(),
    publishDate: z.string().datetime(),
    lastUpdated: z.string().datetime().optional(),
    version: z.string().default('1.0'),
    language: z.enum(['en', 'sw', 'ki', 'luo', 'kln']).default('en'),
    pageCount: z.number().int().positive().optional(),
    fileSize: z.string().optional(),
    isbn: z.string().optional(),
    doi: z.string().optional(),
  }),
  access: z.object({
    level: z.enum(['public', 'registered', 'partners', 'internal']).default('public'),
    requiresRegistration: z.boolean().default(false),
    downloadLimit: z.number().int().positive().optional(),
  }),
  keywords: z.array(z.object({
    keyword: z.string().min(1, 'Keyword cannot be empty'),
  })).optional(),
  featured: z.boolean().default(false),
  published: z.boolean().default(true),
  externalUrl: urlSchema,
  slug: slugSchema,
})

// News validation schema
export const newsSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  subtitle: z.string().max(300, 'Subtitle must be less than 300 characters').optional(),
  summary: z.string().min(1, 'Summary is required').max(300, 'Summary must be less than 300 characters'),
  content: richTextSchema,
  featuredImage: mediaSchema,
  gallery: z.array(z.object({
    image: mediaSchema,
    caption: z.string().optional(),
    credit: z.string().optional(),
  })).optional(),
  category: z.enum([
    'news',
    'press-release',
    'blog-post',
    'event-update',
    'project-update',
    'policy-update',
    'partnership-announcement',
    'success-story',
    'research-update',
  ]),
  status: z.enum(['draft', 'review', 'published', 'archived']).default('draft'),
  publishDate: z.string().datetime(),
  author: z.object({
    name: z.string().min(1, 'Author name is required'),
    role: z.string().optional(),
    organization: z.string().optional(),
    bio: z.string().optional(),
    photo: mediaSchema,
    email: emailSchema.optional(),
    socialLinks: z.array(z.object({
      platform: z.enum(['twitter', 'linkedin', 'facebook', 'website']),
      url: z.string().url('Please enter a valid URL'),
    })).optional(),
  }),
  location: z.object({
    county: z.string().optional(),
    specificLocation: z.string().optional(),
    coordinates: coordinatesSchema,
  }).optional(),
  seo: z.object({
    metaTitle: z.string().max(60, 'Meta title must be less than 60 characters').optional(),
    metaDescription: z.string().max(160, 'Meta description must be less than 160 characters').optional(),
    keywords: z.array(z.object({
      keyword: z.string().min(1, 'Keyword cannot be empty'),
    })).optional(),
    ogImage: mediaSchema,
  }).optional(),
  engagement: z.object({
    allowComments: z.boolean().default(true),
    socialSharing: z.boolean().default(true),
    newsletter: z.boolean().default(false),
  }),
  featured: z.boolean().default(false),
  urgent: z.boolean().default(false),
  tags: z.array(z.object({
    tag: z.string().min(1, 'Tag cannot be empty'),
  })).optional(),
  slug: slugSchema,
})

// Contact submission validation schema
export const contactSubmissionSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: emailSchema,
  phone: phoneSchema,
  organization: z.string().max(200, 'Organization name must be less than 200 characters').optional(),
  role: z.string().max(100, 'Role must be less than 100 characters').optional(),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject must be less than 200 characters'),
  category: z.enum([
    'general',
    'partnership',
    'investment',
    'project-collaboration',
    'media-press',
    'research',
    'training',
    'technical-support',
    'policy-advocacy',
    'community-engagement',
    'feedback',
    'complaint',
    'other',
  ]),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  message: z.string().min(1, 'Message is required').max(2000, 'Message must be less than 2000 characters'),
  location: z.object({
    county: z.string().optional(),
    city: z.string().optional(),
    country: z.string().default('Kenya'),
  }).optional(),
})

// Investment opportunity validation schema
export const investmentOpportunitySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: richTextSchema,
  summary: z.string().min(1, 'Summary is required').max(300, 'Summary must be less than 300 characters'),
  sector: z.enum([
    'natural-products-processing',
    'traditional-medicine',
    'cosmetics-personal-care',
    'food-beverages',
    'agriculture-farming',
    'research-development',
    'technology-innovation',
    'manufacturing',
    'distribution-retail',
    'education-training',
  ]),
  investmentType: z.enum([
    'equity',
    'debt',
    'grant',
    'joint-venture',
    'ppp',
    'impact',
    'crowdfunding',
    'blended-finance',
  ]),
  status: z.enum(['open', 'under-review', 'funded', 'closed', 'on-hold']).default('open'),
  image: mediaSchema,
  gallery: z.array(z.object({
    image: mediaSchema,
    caption: z.string().optional(),
  })).optional(),
  financial: z.object({
    fundingRequired: z.number().positive(),
    currency: z.enum(['KES', 'USD', 'EUR']).default('KES'),
    fundingStages: z.array(z.object({
      stage: z.string().min(1, 'Stage name is required'),
      amount: z.number().positive(),
      timeline: z.string().optional(),
      milestones: z.string().optional(),
    })).optional(),
    useOfFunds: z.array(z.object({
      category: z.string().min(1, 'Category is required'),
      amount: z.number().positive(),
      percentage: z.number().min(0).max(100).optional(),
      description: z.string().optional(),
    })).optional(),
    expectedReturns: z.object({
      roi: z.number().positive().optional(),
      paybackPeriod: z.string().optional(),
      revenueProjections: z.array(z.object({
        year: z.number().int().positive(),
        revenue: z.number().positive(),
        profit: z.number().optional(),
      })).optional(),
    }).optional(),
  }),
  businessModel: z.object({
    valueProposition: richTextSchema,
    targetMarket: richTextSchema,
    competitiveAdvantage: richTextSchema.optional(),
    revenueStreams: z.array(z.object({
      stream: z.string().min(1, 'Revenue stream is required'),
      description: z.string().optional(),
      projectedRevenue: z.number().positive().optional(),
    })).optional(),
    keyPartners: z.array(z.object({
      partner: z.string().min(1, 'Partner name is required'),
      role: z.string().min(1, 'Partner role is required'),
      contribution: z.string().optional(),
    })).optional(),
  }).optional(),
  location: z.object({
    counties: z.array(z.string()).min(1, 'At least one county is required'),
    specificLocation: z.string().optional(),
    coordinates: coordinatesSchema,
  }),
  timeline: z.object({
    applicationDeadline: z.string().datetime().optional(),
    expectedStartDate: z.string().datetime().optional(),
    projectDuration: z.string().optional(),
    milestones: z.array(z.object({
      milestone: z.string().min(1, 'Milestone is required'),
      targetDate: z.string().datetime().optional(),
      description: z.string().optional(),
    })).optional(),
  }).optional(),
  requirements: z.object({
    investorCriteria: z.array(z.object({
      criterion: z.string().min(1, 'Criterion is required'),
      description: z.string().optional(),
      mandatory: z.boolean().default(false),
    })).optional(),
    minimumInvestment: z.number().positive().optional(),
    maximumInvestment: z.number().positive().optional(),
    investorType: z.array(z.enum([
      'individual',
      'institutional',
      'impact',
      'government',
      'dfi',
      'private-equity',
      'venture-capital',
    ])).optional(),
    documentation: z.array(z.object({
      document: z.string().min(1, 'Document name is required'),
      required: z.boolean().default(true),
      description: z.string().optional(),
    })).optional(),
  }).optional(),
  impact: z.object({
    socialImpact: richTextSchema.optional(),
    environmentalImpact: richTextSchema.optional(),
    economicImpact: richTextSchema.optional(),
    beneficiaries: z.number().int().positive().optional(),
    jobsCreated: z.number().int().positive().optional(),
    sdgAlignment: z.array(z.object({
      sdg: z.enum([
        'sdg-1', 'sdg-2', 'sdg-3', 'sdg-4', 'sdg-5',
        'sdg-8', 'sdg-9', 'sdg-10', 'sdg-11', 'sdg-12',
        'sdg-13', 'sdg-15', 'sdg-17',
      ]),
      description: z.string().optional(),
    })).optional(),
  }).optional(),
  team: z.object({
    projectLead: z.object({
      name: z.string().min(1, 'Project lead name is required'),
      role: z.string().min(1, 'Project lead role is required'),
      bio: z.string().optional(),
      photo: mediaSchema,
      email: emailSchema.optional(),
    }).optional(),
    keyPersonnel: z.array(z.object({
      name: z.string().min(1, 'Name is required'),
      role: z.string().min(1, 'Role is required'),
      expertise: z.string().optional(),
      bio: z.string().optional(),
    })).optional(),
  }).optional(),
  applicationProcess: z.object({
    steps: z.array(z.object({
      step: z.string().min(1, 'Step description is required'),
      description: z.string().optional(),
      duration: z.string().optional(),
    })).optional(),
    contactPerson: z.object({
      name: z.string().min(1, 'Contact person name is required'),
      role: z.string().min(1, 'Contact person role is required'),
      email: emailSchema,
      phone: phoneSchema,
    }).optional(),
    applicationForm: mediaSchema,
  }).optional(),
  featured: z.boolean().default(false),
  urgent: z.boolean().default(false),
  tags: z.array(z.object({
    tag: z.string().min(1, 'Tag cannot be empty'),
  })).optional(),
  slug: slugSchema,
})

// Export all schemas
export const validationSchemas = {
  project: projectSchema,
  successStory: successStorySchema,
  resource: resourceSchema,
  news: newsSchema,
  contactSubmission: contactSubmissionSchema,
  investmentOpportunity: investmentOpportunitySchema,
}

// Type inference from schemas
export type ProjectInput = z.infer<typeof projectSchema>
export type SuccessStoryInput = z.infer<typeof successStorySchema>
export type ResourceInput = z.infer<typeof resourceSchema>
export type NewsInput = z.infer<typeof newsSchema>
export type ContactSubmissionInput = z.infer<typeof contactSubmissionSchema>
export type InvestmentOpportunityInput = z.infer<typeof investmentOpportunitySchema>
