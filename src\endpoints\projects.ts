import type { PayloadRequest } from 'payload'
import type { Project } from '@/payload-types'
import { extractTextFromLexical, transformMedia, transformCounty, createGenericCRUDHandlers } from './crud-helpers'

interface TransformedMedia {
  id: string
  url: string
  filename: string
  mimeType: string
  filesize: number
  width?: number
  height?: number
  alt?: string
}

interface TransformedCounty {
  id: string
  name: string
  code: string
}

interface TransformedProject {
  id: string
  title: string
  description: string
  summary: string
  image?: TransformedMedia
  gallery?: TransformedMedia[]
  category: string
  pillar: string
  status: string
  location: {
    counties?: TransformedCounty[]
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  timeline: {
    startDate: string
    endDate?: string
    duration?: string
    milestones?: Array<{
      title: string
      description?: string
      targetDate?: string
      completed: boolean
    }>
  }
  budget?: {
    totalBudget?: number
    currency: string
    fundingSources?: Array<{
      source: string
      amount?: number
      percentage?: number
    }>
  }
  impact?: {
    beneficiaries?: number
    communities?: number
    jobsCreated?: number
    metrics?: Array<{
      metric: string
      value: string
      unit?: string
    }>
  }
  team?: {
    projectManager?: string
    implementingPartners?: Array<{
      partner: any
      role?: string
    }>
    keyPersonnel?: Array<{
      name: string
      role: string
      organization?: string
    }>
  }
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

// Transform project for API response
const transformProject = (project: Project): any => {
  return {
    id: project.id,
    title: project.title,
    description: extractTextFromLexical(project.description),
    summary: project.summary,
    image: transformMedia(project.image),
    gallery: project.gallery?.map(item => ({
      ...transformMedia(item.image),
      caption: item.caption,
    })) || [],
    category: project.category,
    pillar: project.pillar,
    status: project.status,
    location: {
      counties: Array.isArray(project.location?.counties)
        ? project.location.counties.map(transformCounty)
        : [],
      specificLocation: project.location?.specificLocation,
      coordinates: project.location?.coordinates,
    },
    timeline: project.timeline || {},
    budget: project.budget,
    impact: project.impact,
    team: project.team,
    featured: project.featured || false,
    published: project.published || false,
    tags: project.tags?.map(tag => tag.tag).filter(Boolean) || [],
    slug: project.slug || '',
    createdAt: project.createdAt,
    updatedAt: project.updatedAt,
  }
}

interface ProjectsResponse {
  projects: TransformedProject[]
  totalProjects: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}



// Main Projects Handler
export const projectsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      category,
      pillar,
      status,
      featured,
      county,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (category) where.category = { equals: category }
    if (pillar) where.pillar = { equals: pillar }
    if (status) where.status = { equals: status }
    if (featured === 'true') where.featured = { equals: true }
    if (county) {
      where['location.counties'] = { in: [county] }
    }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch projects with populated relationships
    const projectsResult = await payload.find({
      collection: 'projects',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, partners, etc.
    })

    // Transform projects
    const transformedProjects: TransformedProject[] = projectsResult.docs.map(transformProject)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(projectsResult.totalDocs / currentLimit)

    const response: ProjectsResponse = {
      projects: transformedProjects,
      totalProjects: projectsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in projects endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single project by ID or slug
export const projectByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let project
    try {
      project = await payload.findByID({
        collection: 'projects',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'projects',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      project = result.docs[0]
    }

    if (!project) {
      return res.status(404).json({
        error: 'Project not found',
        message: `No project found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!project.published && !req.user) {
      return res.status(404).json({
        error: 'Project not found',
        message: 'Project is not published',
      })
    }

    const transformedProject = transformProject(project)

    res.status(200).json({
      project: transformedProject,
    })
  } catch (error) {
    console.error('Error in project by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Generate CRUD handlers using the generic helper
const { createHandler, updateHandler, deleteHandler } = createGenericCRUDHandlers('projects', 'Project')

export const createProjectHandler = createHandler
export const updateProjectHandler = updateHandler
export const deleteProjectHandler = deleteHandler
