# Payload CMS Collections and API Endpoints

This document provides a comprehensive overview of all Payload CMS collections and their corresponding API endpoints for the NPI platform.

## Collections Overview

### Content Management Collections

1. **Projects** (`projects`)
   - Purpose: Manage project data and metadata
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints: 
     - `GET /api/projects` - List all projects
     - `GET /api/projects/:id` - Get single project

2. **Success Stories** (`success-stories`)
   - Purpose: Manage success story content
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/success-stories` - List all success stories
     - `GET /api/success-stories/:id` - Get single success story

3. **Events** (`events`)
   - Purpose: Manage event information
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/events` - List all events

4. **Media Gallery** (`media-gallery`)
   - Purpose: Manage media gallery items
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/media-gallery` - List all media items
     - `GET /api/media-gallery/:id` - Get single media item

5. **Resources** (`resources`)
   - Purpose: Manage resource files and metadata
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/resources` - List all resources
     - `GET /api/resources/:id` - Get single resource
     - `GET /api/resources/:id/download` - Download resource file

6. **News & Updates** (`news`)
   - Purpose: Manage news articles and updates
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/news` - List all news articles
     - `GET /api/news/:id` - Get single news article

7. **Partnerships** (`partnerships`)
   - Purpose: Manage partnership information
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/partnerships` - List all partnerships
     - `GET /api/partnerships/:id` - Get single partnership

8. **Investment Opportunities** (`investment-opportunities`)
   - Purpose: Manage investment data
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/investment-opportunities` - List all investment opportunities
     - `GET /api/investment-opportunities/:id` - Get single investment opportunity

9. **Partners** (`partners`)
   - Purpose: Manage partner information
   - Admin Interface: ✅ Full CRUD operations
   - API Endpoints:
     - `GET /api/partners` - List all partners
     - `GET /api/partners/:id` - Get single partner

### Form Submission Collections

10. **Contact Submissions** (`contact-submissions`)
    - Purpose: Capture and store contact form submissions
    - Admin Interface: ✅ Full CRUD operations for admins
    - API Endpoints:
      - `GET /api/contact-submissions` - List all submissions (admin only)
      - `POST /api/contact-submissions` - Submit contact form (public)
      - `GET /api/contact-submissions/:id` - Get single submission (admin only)
      - `PUT /api/contact-submissions/:id` - Update submission (admin only)

11. **Partnership Applications** (`partnership-applications`)
    - Purpose: Capture and store partnership application form submissions
    - Admin Interface: ✅ Full CRUD operations for admins
    - API Endpoints:
      - `GET /api/partnership-applications` - List all applications (admin only)
      - `POST /api/partnership-applications` - Submit application (public)
      - `GET /api/partnership-applications/:id` - Get single application (admin only)
      - `PUT /api/partnership-applications/:id` - Update application (admin only)

### Supporting Collections

12. **Media** (`media`)
    - Purpose: File storage and media management
    - Admin Interface: ✅ Full CRUD operations
    - Storage: Database + Vercel Blob (production)

13. **Users** (`users`)
    - Purpose: User authentication and management
    - Admin Interface: ✅ Full CRUD operations

14. **Counties** (`counties`)
    - Purpose: Kenya county data for location references
    - Admin Interface: ✅ Full CRUD operations
    - API Endpoints: Multiple CRUD endpoints available

15. **Speakers** (`speakers`)
    - Purpose: Event speaker information
    - Admin Interface: ✅ Full CRUD operations

16. **Categories** (`categories`)
    - Purpose: Content categorization
    - Admin Interface: ✅ Full CRUD operations

17. **Pages** (`pages`)
    - Purpose: CMS page management
    - Admin Interface: ✅ Full CRUD operations

18. **Posts** (`posts`)
    - Purpose: Blog post management
    - Admin Interface: ✅ Full CRUD operations

## Key Features

### Database Integration
- ✅ All data stored in MongoDB database
- ✅ No mock data in production
- ✅ Auto-generated unique IDs for all collections
- ✅ Images stored directly in database (with Vercel Blob for production)

### Admin Interface
- ✅ Comprehensive CRUD operations for all collections
- ✅ User-friendly admin interface with proper grouping
- ✅ Form submission management with status tracking
- ✅ Rich text editing capabilities
- ✅ Media upload and management

### API Endpoints
- ✅ RESTful API endpoints for all collections
- ✅ Public endpoints for form submissions
- ✅ Admin-only endpoints for sensitive data
- ✅ Proper authentication and authorization
- ✅ Query parameters for filtering and pagination

### Frontend Integration
- ✅ Contact form integrated with API
- ✅ Partnership application form integrated with API
- ✅ Real-time form validation and error handling
- ✅ Success/error message display

## Access Control

### Public Access
- All content collections (read-only)
- Form submission endpoints (POST only)

### Admin Access Required
- All CRUD operations on content collections
- Form submission management
- User management
- Media management

## Next Steps

1. **Testing**: Verify all API endpoints work correctly
2. **Data Migration**: Import existing content into collections
3. **Frontend Integration**: Connect remaining frontend components to APIs
4. **Performance Optimization**: Implement caching and optimization strategies
