'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import {
  FileText,
  Download,
  Calendar,
  Eye,
  Search,
  Filter,
  BookOpen,
  Video,
  Image,
} from 'lucide-react'
import { useGetResourcesQuery } from '@/lib/api/resourcesApi'

interface Resource {
  id: string
  title: string
  description: string
  type: 'report' | 'guide' | 'policy' | 'research' | 'video' | 'presentation' | 'toolkit'
  category: string
  date: string
  fileSize?: string
  downloadCount: number
  featured?: boolean
  url: string
  previewUrl?: string
  tags: string[]
}

interface NPIResourcesLibraryProps {
  title?: string
  description?: string
}

export const NPIResourcesLibraryBlock: React.FC<NPIResourcesLibraryProps> = ({
  title = 'Resources & Publications',
  description = 'Access comprehensive resources, research publications, policy documents, and educational materials to support natural products development and traditional knowledge preservation.',
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedType, setSelectedType] = useState('All Types')

  const {
    data: resources,
    isLoading,
    error,
    refetch,
  } = useGetResourcesQuery({
    limit: 1000,
    ...(selectedCategory !== 'All Categories' && { category: selectedCategory }),
    ...(selectedType !== 'All Types' && { type: selectedType }),
  })

  console.log({ resources, isLoading, error, refetch })

  const categories = [
    'All Categories',
    ...Array.from(new Set(resources?.docs?.map((resource: Resource) => resource.category))),
  ]
  const types = [
    'All Types',
    ...Array.from(new Set(resources?.docs?.map((resource: Resource) => resource.type))),
  ]

  const filteredResources = resources?.docs?.filter((resource: Resource) => {
    const matchesSearch =
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory =
      selectedCategory === 'All Categories' || resource.category === selectedCategory
    const matchesType = selectedType === 'All Types' || resource.type === selectedType

    return matchesSearch && matchesCategory && matchesType
  })

  const featuredResources = filteredResources?.filter((resource: Resource) => resource.featured)
  const regularResources = filteredResources?.filter((resource: Resource) => !resource.featured)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-5 h-5" />
      case 'presentation':
        return <Image className="w-5 h-5" />
      default:
        return <FileText className="w-5 h-5" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'report':
        return 'bg-[#25718A] text-white'
      case 'guide':
        return 'bg-[#725242] text-white'
      case 'policy':
        return 'bg-[#8A3E25] text-white'
      case 'research':
        return 'bg-[#25718A] text-white'
      case 'video':
        return 'bg-[#8A3E25] text-white'
      case 'presentation':
        return 'bg-[#725242] text-white'
      case 'toolkit':
        return 'bg-[#25718A] text-white'
      default:
        return 'bg-[#725242] text-white'
    }
  }

  return (
    <NPISection className="bg-[#FFFFFF]">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-black">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Search and Filters */}
      <NPICard
        className="mb-8 border-[#725242] shadow-lg"
        style={{ borderRadius: '0', backgroundColor: '#FFFFFF' }}
      >
        <NPICardContent className="p-6">
          {/* Search Bar */}
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#25718A] w-5 h-5" />
              <input
                type="text"
                placeholder="Search resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] focus:border-[#25718A] font-npi text-black bg-white"
                style={{ borderRadius: '0' }}
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#725242]" />
            <span className="font-medium font-npi text-[#25718A]">Filter by:</span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#25718A]">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-black bg-white"
                style={{ borderRadius: '0' }}
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#25718A]">Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-black bg-white"
                style={{ borderRadius: '0' }}
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    {type === 'All Types' ? type : type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Loading Skeleton */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden rounded-lg bg-white shadow animate-pulse aspect-square flex flex-col"
            >
              {/* Image placeholder */}
              <div className="h-32 w-full bg-gray-200"></div>

              {/* Card header placeholder */}
              <div className="p-3 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>

              {/* Card content placeholder */}
              <div className="p-3 space-y-2 flex-1">
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>

              {/* Footer placeholder */}
              <div className="p-3 flex gap-2">
                <div className="h-8 bg-gray-200 rounded flex-1"></div>
                <div className="h-8 bg-gray-200 rounded w-8"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-red-500 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L4.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-800">Something went wrong</h3>
          <p className="text-gray-600 mt-1">We couldn’t load the resources.</p>
          <button
            onClick={() => refetch()}
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      )}

      {!isLoading && !error && (!filteredResources || filteredResources?.length === 0) && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 17v-4H5v4H3V7h2v4h4V7h2v10H9zm12 0v-4h-4v4h-2V7h2v4h4V7h2v10h-2z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-800">No resources available</h3>
          <p className="text-gray-600 mt-1">Check back later for new resources.</p>
        </div>
      )}

      {/* Featured Resources */}
      {featuredResources?.length > 0 && (
        <div className="mb-12" id="featured-resources">
          <h3 className="text-2xl font-bold mb-6 font-npi flex items-center gap-2 text-black">
            <BookOpen className="w-6 h-6 text-[#725242]" />
            Featured Resources
          </h3>
          <div className="grid grid-cols-4 gap-6">
            {featuredResources.slice(0, 4).map((resource: Resource, index: number) => (
              <NPICard
                key={resource.id}
                className={`overflow-hidden hover:shadow-xl transition-all duration-300 aspect-square flex flex-col border-2 ${
                  index % 2 === 0
                    ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                    : 'bg-white border-[#725242] hover:border-[#8A3E25]'
                }`}
              >
                <NPICardHeader className="flex-shrink-0">
                  <div className="flex items-center justify-between mb-2">
                    <span
                      className={`px-2 py-1 text-xs font-medium ${getTypeColor(resource.type)}`}
                    >
                      {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                    </span>
                    <span className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium">
                      Featured
                    </span>
                  </div>
                  <NPICardTitle className="text-base leading-tight text-black line-clamp-2 mb-2">
                    {resource.title}
                  </NPICardTitle>
                  <div className="text-xs text-[#725242] font-npi">{resource.category}</div>
                </NPICardHeader>

                <NPICardContent className="flex-grow flex flex-col">
                  <div className="flex items-center gap-2 text-xs text-[#725242] mb-4 flex-grow">
                    <Calendar className="w-3 h-3" />
                    <span>{new Date(resource.date).toLocaleDateString()}</span>
                  </div>
                </NPICardContent>

                <NPICardFooter className="flex gap-2 flex-shrink-0">
                  <NPIButton
                    asChild
                    variant="primary"
                    className="flex-1 bg-[#8A3E25] hover:bg-[#8A3E25] text-white"
                    style={{ borderRadius: '0' }}
                  >
                    <Link href={resource.url} target="_blank">
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Resources */}
      <div className="grid grid-cols-4 gap-6">
        {regularResources?.map((resource: Resource) => (
          <NPICard
            key={resource.id}
            className="hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-[#725242] border-2 border-[#725242] aspect-square flex flex-col"
            style={{ backgroundColor: 'white' }}
          >
            <NPICardHeader className="flex-shrink-0">
              <div className="flex items-center gap-3 mb-3">
                <div
                  className="w-8 h-8 bg-[#725242]/10 flex items-center justify-center text-[#725242]"
                  style={{ borderRadius: '0' }}
                >
                  {getTypeIcon(resource.type)}
                </div>
                <div>
                  <span
                    className={`px-2 py-1 text-xs font-medium ${getTypeColor(resource.type)}`}
                    style={{ borderRadius: '0' }}
                  >
                    {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                  </span>
                </div>
              </div>
              <NPICardTitle className="text-lg leading-tight text-black line-clamp-2">
                {resource.title}
              </NPICardTitle>
              <div className="text-sm text-[#725242] font-medium font-npi">{resource.category}</div>
            </NPICardHeader>

            <NPICardContent className="flex-grow flex flex-col">
              <div className="flex items-center gap-2 text-xs text-[#725242] mb-4 flex-grow">
                <Calendar className="w-3 h-3" />
                <span>{new Date(resource.date).toLocaleDateString()}</span>
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-2 flex-shrink-0">
              <NPIButton
                asChild
                variant="outline"
                className="flex-1 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
                size="sm"
                style={{ borderRadius: '0' }}
              >
                <Link href={resource.url} target="_blank">
                  <Download className="w-3 h-3 mr-2" />
                  Download
                </Link>
              </NPIButton>
              {resource.previewUrl && (
                <NPIButton
                  asChild
                  variant="ghost"
                  size="sm"
                  className="text-[#25718A] hover:bg-[#25718A]/10"
                  style={{ borderRadius: '0' }}
                >
                  <Link href={resource.previewUrl} target="_blank">
                    <Eye className="w-3 h-3" />
                  </Link>
                </NPIButton>
              )}
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#725242] font-npi">
          Showing {filteredResources?.length} of {resources?.docs?.length} resources
        </p>
      </div>
    </NPISection>
  )
}
