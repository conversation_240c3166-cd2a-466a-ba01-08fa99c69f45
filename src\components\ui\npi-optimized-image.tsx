'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { cn } from '@/utilities/ui'

interface PayloadImageSizes {
  thumbnail?: { url: string; width: number; height: number }
  square?: { url: string; width: number; height: number }
  small?: { url: string; width: number; height: number }
  medium?: { url: string; width: number; height: number }
  large?: { url: string; width: number; height: number }
  xlarge?: { url: string; width: number; height: number }
  hero?: { url: string; width: number; height: number }
  og?: { url: string; width: number; height: number }
  card?: { url: string; width: number; height: number }
}

interface PayloadImage {
  url: string
  alt?: string
  width?: number
  height?: number
  sizes?: PayloadImageSizes
}

interface NPIOptimizedImageProps {
  image: PayloadImage | string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  fill?: boolean
  sizes?: string
  quality?: number
  preferredSize?: keyof PayloadImageSizes
  fallbackSrc?: string
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
}

export const NPIOptimizedImage: React.FC<NPIOptimizedImageProps> = ({
  image,
  alt,
  width,
  height,
  className,
  priority = false,
  fill = false,
  sizes,
  quality = 85,
  preferredSize = 'medium',
  fallbackSrc = '/assets/product 1.jpg',
  objectFit = 'cover',
  objectPosition = 'center',
  loading = 'lazy',
  onLoad,
  onError,
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState<string>('')

  // Extract image source with optimization
  const getOptimizedImageSrc = (img: PayloadImage | string): string => {
    if (typeof img === 'string') {
      return img
    }

    // Try to get the preferred size first
    if (img.sizes?.[preferredSize]?.url) {
      return img.sizes[preferredSize].url
    }

    // Fallback to other sizes in order of preference
    const sizePreference: (keyof PayloadImageSizes)[] = [
      'card', 'medium', 'small', 'large', 'square', 'thumbnail'
    ]

    for (const size of sizePreference) {
      if (img.sizes?.[size]?.url) {
        return img.sizes[size].url
      }
    }

    // Final fallback to main image URL
    return img.url || fallbackSrc
  }

  // Get image dimensions if available
  const getImageDimensions = (img: PayloadImage | string) => {
    if (typeof img === 'string') {
      return { width: undefined, height: undefined }
    }

    if (img.sizes?.[preferredSize]) {
      return {
        width: img.sizes[preferredSize].width,
        height: img.sizes[preferredSize].height
      }
    }

    return {
      width: img.width,
      height: img.height
    }
  }

  const imageSrc = currentSrc || getOptimizedImageSrc(image)
  const { width: imgWidth, height: imgHeight } = getImageDimensions(image)
  const imageAlt = typeof image === 'object' ? image.alt || alt : alt

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    if (!hasError && imageSrc !== fallbackSrc) {
      setHasError(true)
      setCurrentSrc(fallbackSrc)
    } else {
      setHasError(true)
      onError?.()
    }
  }

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || (
    fill 
      ? '100vw'
      : `(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw`
  )

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Loading placeholder */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
        </div>
      )}

      {/* Error placeholder */}
      {hasError && imageSrc === fallbackSrc && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">Image not available</p>
          </div>
        </div>
      )}

      <Image
        src={imageSrc}
        alt={imageAlt}
        width={fill ? undefined : (width || imgWidth)}
        height={fill ? undefined : (height || imgHeight)}
        fill={fill}
        sizes={responsiveSizes}
        quality={quality}
        priority={priority}
        loading={priority ? undefined : loading}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          objectFit === 'cover' && 'object-cover',
          objectFit === 'contain' && 'object-contain',
          objectFit === 'fill' && 'object-fill',
          objectFit === 'none' && 'object-none',
          objectFit === 'scale-down' && 'object-scale-down',
        )}
        style={{ objectPosition }}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  )
}
