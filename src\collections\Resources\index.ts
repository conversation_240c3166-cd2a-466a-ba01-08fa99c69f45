import { CollectionConfig } from 'payload'
import { anyone } from '@/access/anyone'
import { authenticated } from '@/access/authenticated'
import { slugField } from '@/fields/slug'

export const Resources: CollectionConfig = {
  slug: 'resources',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  labels: {
    singular: 'Resource',
    plural: 'Resources',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'category', 'featured', 'date'],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Report', value: 'report' },
        { label: 'Guide', value: 'guide' },
        { label: 'Policy', value: 'policy' },
        { label: 'Toolkit', value: 'toolkit' },
        { label: 'Research', value: 'research' },
        { label: 'Video', value: 'video' },
        { label: 'Presentation', value: 'presentation' },
      ],
    },
    {
      name: 'category',
      type: 'text',
      required: true,
    },
    {
      name: 'date',
      type: 'date',
      required: true,
      admin: {
        date: { pickerAppearance: 'dayOnly' },
      },
    },
    {
      name: 'fileSize',
      type: 'text',
      required: false,
    },
    {
      name: 'downloadCount',
      type: 'number',
      defaultValue: 0,
      required: true,
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'url',
      type: 'text',
      required: true,
    },
    {
      name: 'previewUrl',
      type: 'text',
      required: false,
    },
    {
      name: 'tags',
      type: 'array',
      required: false,
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
    },
    ...slugField(),
  ],
}
